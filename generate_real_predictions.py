"""
生成真实的LSTM和LSTM+AKF预测数据用于视频制作
"""

import asyncio
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import warnings
from collections import deque

warnings.filterwarnings("ignore")

# 简化的LSTM模型
class DisplacementLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

class AdaptiveKalmanFilter:
    """自适应卡尔曼滤波器"""
    def __init__(self, process_variance=1e-5, measurement_variance=1e-3):
        self.process_variance = process_variance
        self.measurement_variance = measurement_variance
        self.posteri_estimate = 0.0
        self.posteri_error_estimate = 1.0
        self.innovation_history = deque(maxlen=10)
        
    def update(self, measurement):
        # 预测步骤
        priori_estimate = self.posteri_estimate
        priori_error_estimate = self.posteri_error_estimate + self.process_variance
        
        # 更新步骤
        innovation = measurement - priori_estimate
        self.innovation_history.append(abs(innovation))
        
        # 自适应调整测量噪声
        if len(self.innovation_history) >= 5:
            recent_innovation = np.mean(list(self.innovation_history)[-5:])
            self.measurement_variance = max(1e-6, min(1e-2, recent_innovation * 0.1))
        
        innovation_covariance = priori_error_estimate + self.measurement_variance
        kalman_gain = priori_error_estimate / innovation_covariance
        
        self.posteri_estimate = priori_estimate + kalman_gain * innovation
        self.posteri_error_estimate = (1 - kalman_gain) * priori_error_estimate
        
        return self.posteri_estimate

def train_lstm_model(data, window_size=10, epochs=50):
    """训练LSTM模型"""
    print("🤖 训练LSTM模型...")
    
    # 数据预处理
    scaler = MinMaxScaler()
    scaled_data = scaler.fit_transform(data.reshape(-1, 1)).flatten()
    
    # 创建序列
    X, y = create_sequences(scaled_data, window_size)
    
    # 转换为PyTorch张量
    X_tensor = torch.FloatTensor(X).unsqueeze(-1)
    y_tensor = torch.FloatTensor(y)
    
    # 创建模型
    model = DisplacementLSTM()
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # 训练
    model.train()
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X_tensor).squeeze()
        loss = criterion(outputs, y_tensor)
        loss.backward()
        optimizer.step()
        
        if (epoch + 1) % 10 == 0:
            print(f"   Epoch {epoch+1}/{epochs}, Loss: {loss.item():.6f}")
    
    return model, scaler, window_size

def generate_predictions(data, model, scaler, window_size):
    """生成LSTM预测"""
    print("📊 生成LSTM预测...")
    
    model.eval()
    predictions = []
    
    # 预处理数据
    scaled_data = scaler.transform(data.reshape(-1, 1)).flatten()
    
    with torch.no_grad():
        for i in range(window_size, len(scaled_data)):
            # 获取输入序列
            seq = scaled_data[i-window_size:i]
            seq_tensor = torch.FloatTensor(seq).unsqueeze(0).unsqueeze(-1)
            
            # 预测
            pred = model(seq_tensor).item()
            predictions.append(pred)
    
    # 反归一化
    predictions = np.array(predictions)
    predictions = scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()
    
    return predictions

def apply_adaptive_kalman_filter(lstm_predictions):
    """应用自适应卡尔曼滤波"""
    print("🔧 应用自适应卡尔曼滤波...")
    
    akf = AdaptiveKalmanFilter()
    filtered_predictions = []
    
    for pred in lstm_predictions:
        filtered_pred = akf.update(pred)
        filtered_predictions.append(filtered_pred)
    
    return np.array(filtered_predictions)

def main():
    """主函数"""
    print("🎬 生成真实预测数据用于视频制作")
    print("=" * 50)
    
    # 加载数据
    try:
        print("📊 加载v2.txt数据...")
        v2_data = pd.read_csv('v2.txt', sep='\t', encoding='utf-8')
        original_data = v2_data.iloc[:, 1].values
        print(f"✅ 成功加载数据，数据点数: {len(original_data)}")
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return
    
    # 使用前1500个点进行训练和预测（保持合理的计算时间）
    data_subset = original_data[:1500]
    
    # 训练LSTM模型
    model, scaler, window_size = train_lstm_model(data_subset)
    
    # 生成LSTM预测
    lstm_predictions = generate_predictions(data_subset, model, scaler, window_size)
    
    # 应用自适应卡尔曼滤波
    akf_predictions = apply_adaptive_kalman_filter(lstm_predictions)
    
    # 对齐数据长度
    original_aligned = data_subset[window_size:]
    
    # 计算性能指标
    lstm_mae = np.mean(np.abs(original_aligned - lstm_predictions))
    akf_mae = np.mean(np.abs(original_aligned - akf_predictions))
    improvement = ((lstm_mae - akf_mae) / lstm_mae * 100)
    
    print(f"\n📈 预测性能:")
    print(f"   - LSTM MAE: {lstm_mae:.6f} μm")
    print(f"   - AKF MAE: {akf_mae:.6f} μm")
    print(f"   - 改进幅度: {improvement:.2f}%")
    
    # 保存结果
    results_df = pd.DataFrame({
        'original': original_aligned,
        'lstm_predicted': lstm_predictions,
        'akf_predicted': akf_predictions
    })
    
    results_df.to_csv('lstm_vs_akf_comparison_data.csv', index=False)
    print(f"✅ 预测数据已保存到: lstm_vs_akf_comparison_data.csv")
    print(f"   - 数据点数: {len(results_df)}")

if __name__ == "__main__":
    main()
