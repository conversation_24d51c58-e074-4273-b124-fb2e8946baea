import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from scipy import signal
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def generate_smooth_vibration_data(duration=60, sampling_rate=100, save_to_csv=True):
    """
    生成一组波动性增强的振动数据

    参数:
    duration: 持续时间(秒)
    sampling_rate: 采样率(Hz)
    save_to_csv: 是否保存为CSV文件

    返回:
    time: 时间数组
    displacement: 位移数据
    velocity: 速度数据
    acceleration: 加速度数据

    特征:
    - 增加了多个频率成分
    - 增大了振幅和噪声水平
    - 添加了间歇性波动和幅度调制
    - 保留了更多高频成分
    """
    
    # 生成时间序列
    t = np.linspace(0, duration, int(duration * sampling_rate))
    
    # 生成适度波动性增强的振动信号
    # 保持可预测性的同时增加复杂度

    # 主要频率成分 (适度增加频率)
    f1 = 0.5   # 0.5 Hz - 主要振动频率
    f2 = 1.2   # 1.2 Hz - 次要频率
    f3 = 2.0   # 2.0 Hz - 高频成分
    f4 = 2.8   # 2.8 Hz - 适度高频成分 (降低频率)
    f5 = 0.8   # 0.8 Hz - 额外低频成分

    # 生成位移信号 (适度增大幅度，保持可预测性)
    displacement = (
        1.0 * np.sin(2 * np.pi * f1 * t) +           # 主要低频成分 (适度幅度)
        0.5 * np.sin(2 * np.pi * f2 * t + np.pi/4) + # 次要频率成分 (适度幅度)
        0.3 * np.sin(2 * np.pi * f3 * t + np.pi/3) + # 高频成分 (适度幅度)
        0.2 * np.sin(2 * np.pi * f4 * t + np.pi/2) + # 高频成分 (降低幅度)
        0.4 * np.sin(2 * np.pi * f5 * t + np.pi/6) + # 额外低频成分 (适度幅度)
        0.08 * np.random.normal(0, 1, len(t))        # 适度噪声水平
    )

    # 添加渐进性波动 (模拟缓慢变化的振动)
    for i in range(len(t)):
        # 在特定时间段添加渐进的波动变化
        if 2 < t[i] < 4:
            factor = 0.5 * (1 - np.cos(np.pi * (t[i] - 2) / 2))  # 渐进增强
            displacement[i] += factor * 0.3 * np.sin(2 * np.pi * 3.0 * t[i])
        elif 6 < t[i] < 8:
            factor = 0.5 * (1 - np.cos(np.pi * (t[i] - 6) / 2))  # 渐进增强
            displacement[i] += factor * 0.25 * np.sin(2 * np.pi * 3.5 * t[i])

    # 添加轻度调制波动 (幅度调制)
    modulation = 1 + 0.15 * np.sin(2 * np.pi * 0.25 * t)  # 降低调制强度
    displacement = displacement * modulation

    # 添加缓慢的趋势变化 (模拟温度漂移等)
    trend = 0.1 * np.sin(2 * np.pi * 0.02 * t)  # 适度趋势变化幅度
    displacement += trend
    
    # 应用适度低通滤波器平衡波动和可预测性
    # 设计巴特沃斯低通滤波器
    nyquist = sampling_rate / 2
    cutoff = 6.0  # 适中的截止频率，保留主要频率成分
    order = 3     # 适中的滤波器阶数
    b, a = signal.butter(order, cutoff / nyquist, btype='low')
    displacement = signal.filtfilt(b, a, displacement)
    
    # 计算速度 (位移的一阶导数)
    velocity = np.gradient(displacement, t)
    
    # 计算加速度 (速度的一阶导数)
    acceleration = np.gradient(velocity, t)
    
    # 创建数据字典
    data = {
        'time': t,
        'displacement': displacement,
        'velocity': velocity,
        'acceleration': acceleration
    }
    
    # 保存为CSV文件
    if save_to_csv:
        df = pd.DataFrame(data)
        df.to_csv('smooth_vibration_data.csv', index=False)
        print(f"波动性增强的振动数据已保存到 smooth_vibration_data.csv")
        print(f"数据点数: {len(t)}")
        print(f"时间范围: {t[0]:.2f} - {t[-1]:.2f} 秒")
        print(f"位移范围: {np.min(displacement):.3f} - {np.max(displacement):.3f}")
        print(f"速度范围: {np.min(velocity):.3f} - {np.max(velocity):.3f}")
        print(f"加速度范围: {np.min(acceleration):.3f} - {np.max(acceleration):.3f}")
        print(f"数据特征: 多频率成分、间歇性波动、幅度调制")
    
    return t, displacement, velocity, acceleration

def plot_vibration_data(t, displacement, velocity, acceleration):
    """
    绘制振动数据的时域图
    """
    fig, axes = plt.subplots(3, 1, figsize=(12, 10))
    
    # 位移图
    axes[0].plot(t, displacement, 'b-', linewidth=1.5, label='位移')
    axes[0].set_ylabel('位移 (mm)')
    axes[0].set_title('波动性增强的振动数据 - 时域分析')
    axes[0].grid(True, alpha=0.3)
    axes[0].legend()
    
    # 速度图
    axes[1].plot(t, velocity, 'g-', linewidth=1.5, label='速度')
    axes[1].set_ylabel('速度 (mm/s)')
    axes[1].grid(True, alpha=0.3)
    axes[1].legend()
    
    # 加速度图
    axes[2].plot(t, acceleration, 'r-', linewidth=1.5, label='加速度')
    axes[2].set_xlabel('时间 (s)')
    axes[2].set_ylabel('加速度 (mm/s²)')
    axes[2].grid(True, alpha=0.3)
    axes[2].legend()
    
    plt.tight_layout()
    plt.savefig('smooth_vibration_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def analyze_frequency_spectrum(t, displacement):
    """
    分析振动数据的频域特性
    """
    # 计算FFT
    sampling_rate = 1 / (t[1] - t[0])
    n = len(displacement)
    frequencies = np.fft.fftfreq(n, 1/sampling_rate)
    fft_values = np.fft.fft(displacement)
    
    # 只取正频率部分
    positive_freq_idx = frequencies > 0
    frequencies = frequencies[positive_freq_idx]
    magnitude = np.abs(fft_values[positive_freq_idx])
    
    # 绘制频谱图
    plt.figure(figsize=(10, 6))
    plt.semilogy(frequencies, magnitude, 'b-', linewidth=1.5)
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值')
    plt.title('波动性增强的振动数据 - 频域分析')
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 10)  # 只显示0-10Hz的频率范围
    plt.savefig('smooth_vibration_frequency.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 找出主要频率成分
    peak_indices = signal.find_peaks(magnitude, height=np.max(magnitude)*0.1)[0]
    main_frequencies = frequencies[peak_indices]
    main_magnitudes = magnitude[peak_indices]
    
    print("\n主要频率成分:")
    for freq, mag in zip(main_frequencies[:5], main_magnitudes[:5]):
        print(f"频率: {freq:.2f} Hz, 幅值: {mag:.2f}")

def generate_custom_smooth_vibration(base_freq=0.8, amplitude=1.0, duration=60, noise_level=0.02):
    """
    生成自定义参数的平缓振动数据
    
    参数:
    base_freq: 基础频率 (Hz)
    amplitude: 基础幅度
    duration: 持续时间 (秒)
    noise_level: 噪声水平 (0-1)
    """
    sampling_rate = 100
    t = np.linspace(0, duration, int(duration * sampling_rate))
    
    # 生成更平缓的振动
    displacement = (
        amplitude * np.sin(2 * np.pi * base_freq * t) +
        amplitude * 0.3 * np.sin(2 * np.pi * base_freq * 1.5 * t + np.pi/6) +
        amplitude * 0.1 * np.sin(2 * np.pi * base_freq * 2.2 * t + np.pi/3) +
        noise_level * amplitude * np.random.normal(0, 1, len(t))
    )
    
    # 应用更强的平滑滤波
    nyquist = sampling_rate / 2
    cutoff = base_freq * 3  # 截止频率设为基础频率的3倍
    order = 6
    b, a = signal.butter(order, cutoff / nyquist, btype='low')
    displacement = signal.filtfilt(b, a, displacement)
    
    return t, displacement

if __name__ == "__main__":
    print("生成波动性增强的振动数据...")

    # 生成波动性增强的振动数据 - 10秒，1000个数据点
    time, disp, vel, acc = generate_smooth_vibration_data(duration=10, sampling_rate=100)
    
    # 绘制时域图
    plot_vibration_data(time, disp, vel, acc)
    
    # 分析频域特性
    analyze_frequency_spectrum(time, disp)
    
    # 生成额外的自定义平缓振动示例
    print("\n生成自定义平缓振动示例...")
    t_custom, disp_custom = generate_custom_smooth_vibration(
        base_freq=0.6,
        amplitude=0.8,
        duration=10,
        noise_level=0.01
    )
    
    # 绘制对比图
    plt.figure(figsize=(12, 8))

    plt.subplot(2, 1, 1)
    plt.plot(time, disp, 'b-', linewidth=1.5, label='波动性增强振动')
    plt.ylabel('位移 (mm)')
    plt.title('波动性增强振动数据分析')
    plt.grid(True, alpha=0.3)
    plt.legend()

    # 第二张图：频域信息图
    plt.subplot(2, 1, 2)
    # 计算FFT
    sampling_rate = 100
    n = len(disp)
    frequencies = np.fft.fftfreq(n, 1/sampling_rate)
    fft_values = np.fft.fft(disp)

    # 只取正频率部分
    positive_freq_idx = frequencies > 0
    frequencies_pos = frequencies[positive_freq_idx]
    magnitude = np.abs(fft_values[positive_freq_idx])

    plt.semilogy(frequencies_pos, magnitude, 'r-', linewidth=1.5, label='频域幅值谱')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值')
    plt.title('频域分析')
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 10)  # 只显示0-10Hz的频率范围
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('smooth_vibration_time_frequency.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("\n适度波动性增强的振动数据生成完成！")
    print("数据特征:")
    print("- 多频率成分: 0.5, 0.8, 1.2, 2.0, 2.8 Hz")
    print("- 渐进性波动: 在2-4s, 6-8s时间段")
    print("- 幅度调制: 0.25 Hz调制频率")
    print("- 适度噪声: 0.08的噪声水平")
    print("- 优化设计: 平衡复杂性和可预测性")
    print("\n生成的文件:")
    print("- smooth_vibration_data.csv (数据文件)")
    print("- smooth_vibration_analysis.png (时域分析图)")
    print("- smooth_vibration_frequency.png (频域分析图)")
    print("- smooth_vibration_time_frequency.png (时域-频域综合图)")
