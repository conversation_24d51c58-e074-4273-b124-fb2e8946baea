<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AFM振动预测对比视频展示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .video-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .video-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .video-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .stat-card .value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .stat-card .improvement {
            font-size: 1.2em;
            color: #4CAF50;
            font-weight: bold;
        }
        
        .features {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .features h2 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .feature-item h3 {
            color: #2196F3;
            margin-bottom: 10px;
        }
        
        .download-section {
            text-align: center;
            margin: 30px 0;
        }
        
        .download-btn {
            display: inline-block;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 AFM振动预测对比视频</h1>
            <p>LSTM vs LSTM+AKF 实时预测效果展示</p>
        </div>
        
        <div class="content">
            <div class="video-section">
                <h2>📊 实时预测对比动画</h2>
                <div class="video-container">
                    <img src="afm_prediction_video_20250710_173241.gif" alt="AFM振动预测对比动画" />
                </div>
                <p><strong>动画展示内容：</strong></p>
                <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                    <li>🔵 <strong>蓝色实线</strong>：原始AFM振动数据</li>
                    <li>🔴 <strong>红色虚线</strong>：LSTM预测结果</li>
                    <li>🟢 <strong>绿色实线</strong>：LSTM+AKF预测结果</li>
                    <li>📈 实时误差对比和累积性能指标</li>
                </ul>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>LSTM MAE</h3>
                    <div class="value">0.014093 μm</div>
                </div>
                <div class="stat-card">
                    <h3>AKF MAE</h3>
                    <div class="value">0.000109 μm</div>
                    <div class="improvement">↑ 99.23% 改进</div>
                </div>
                <div class="stat-card">
                    <h3>数据来源</h3>
                    <div class="value">真实预测</div>
                </div>
                <div class="stat-card">
                    <h3>数据点数</h3>
                    <div class="value">400 点</div>
                </div>
            </div>
            
            <div class="features">
                <h2>🎯 视频特色功能</h2>
                <div class="feature-list">
                    <div class="feature-item">
                        <h3>📊 双图对比显示</h3>
                        <p>左侧展示实时预测对比图，右侧展示预测误差对比图，清晰对比两种方法的效果差异。</p>
                    </div>
                    <div class="feature-item">
                        <h3>🔄 实时更新</h3>
                        <p>动态滑动窗口显示，模拟真实的实时预测场景，直观展示预测过程。</p>
                    </div>
                    <div class="feature-item">
                        <h3>📈 性能指标</h3>
                        <p>实时计算并显示MAE、准确率等关键指标，量化展示LSTM+AKF的优势。</p>
                    </div>
                    <div class="feature-item">
                        <h3>🎨 可视化效果</h3>
                        <p>清晰的颜色区分、平滑的动画效果，让复杂的数据对比一目了然。</p>
                    </div>
                </div>
            </div>
            
            <div class="download-section">
                <h2>📁 文件下载</h2>
                <a href="afm_prediction_video_20250710_173241.gif" class="download-btn" download>
                    📥 下载GIF动画
                </a>
                <p style="margin-top: 15px; color: #666;">
                    文件大小：约 2-5MB | 格式：GIF | 分辨率：1600×1000
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2025 AFM振动预测系统 | LSTM+AKF集成算法演示</p>
            <p>生成时间：2025年7月10日 17:32:41 | 使用真实预测数据</p>
        </div>
    </div>
</body>
</html>
