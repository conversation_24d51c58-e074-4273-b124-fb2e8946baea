"""
计算vib1.txt前20000个点预测的详细准确率分析
包含多种准确率指标和可视化分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings

# 忽略警告
warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    print("使用默认字体")

def load_prediction_results(filename='vib1_20k_improved_integrated_lstm_kalman_results.csv'):
    """加载预测结果数据"""
    try:
        data = pd.read_csv(filename)
        print(f"成功加载预测结果文件: {filename}")
        print(f"数据点数: {len(data)}")
        return data
    except FileNotFoundError:
        print(f"错误: 未找到文件 {filename}")
        return None
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def calculate_accuracy_metrics(y_true, y_pred, method_name="预测方法"):
    """计算详细的准确率指标"""
    # 基本误差指标
    mae = mean_absolute_error(y_true, y_pred)
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    
    # 相对误差指标
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100  # 平均绝对百分比误差
    
    # 决定系数
    r2 = r2_score(y_true, y_pred)
    
    # 相关系数
    correlation = np.corrcoef(y_true, y_pred)[0, 1]
    
    # 最大误差
    max_error = np.max(np.abs(y_true - y_pred))
    
    # 准确率（基于相对误差阈值）
    relative_errors = np.abs((y_true - y_pred) / y_true) * 100
    accuracy_1percent = np.sum(relative_errors <= 1.0) / len(relative_errors) * 100
    accuracy_5percent = np.sum(relative_errors <= 5.0) / len(relative_errors) * 100
    accuracy_10percent = np.sum(relative_errors <= 10.0) / len(relative_errors) * 100
    
    # 标准化均方根误差 (NRMSE)
    nrmse = rmse / (np.max(y_true) - np.min(y_true)) * 100
    
    # 平均偏差
    bias = np.mean(y_pred - y_true)
    
    metrics = {
        'method_name': method_name,
        'mae': mae,
        'mse': mse,
        'rmse': rmse,
        'mape': mape,
        'r2_score': r2,
        'correlation': correlation,
        'max_error': max_error,
        'accuracy_1percent': accuracy_1percent,
        'accuracy_5percent': accuracy_5percent,
        'accuracy_10percent': accuracy_10percent,
        'nrmse': nrmse,
        'bias': bias,
        'data_points': len(y_true)
    }
    
    return metrics

def print_accuracy_report(lstm_metrics, kalman_metrics):
    """打印详细的准确率报告"""
    print("\n" + "="*80)
    print("详细准确率分析报告")
    print("="*80)

    print(f"\n数据集信息:")
    print(f"  测试数据点数: {lstm_metrics['data_points']}")
    print(f"  数据来源: vib1.txt前20000个点的预测结果")

    print(f"\n1. 基本误差指标:")
    print("指标                   LSTM预测                    集成LSTM-卡尔曼                改善程度")
    print("-" * 90)

    # MAE
    mae_improvement = (lstm_metrics['mae'] - kalman_metrics['mae']) / lstm_metrics['mae'] * 100
    lstm_mae_str = f"{lstm_metrics['mae']:.2e}"
    kalman_mae_str = f"{kalman_metrics['mae']:.2e}"
    print(f"平均绝对误差(MAE)      {lstm_mae_str:<25} {kalman_mae_str:<25} {mae_improvement:.2f}%")

    # RMSE
    rmse_improvement = (lstm_metrics['rmse'] - kalman_metrics['rmse']) / lstm_metrics['rmse'] * 100
    lstm_rmse_str = f"{lstm_metrics['rmse']:.2e}"
    kalman_rmse_str = f"{kalman_metrics['rmse']:.2e}"
    print(f"均方根误差(RMSE)       {lstm_rmse_str:<25} {kalman_rmse_str:<25} {rmse_improvement:.2f}%")

    # MAPE
    mape_improvement = (lstm_metrics['mape'] - kalman_metrics['mape']) / lstm_metrics['mape'] * 100
    lstm_mape_str = f"{lstm_metrics['mape']:.4f}%"
    kalman_mape_str = f"{kalman_metrics['mape']:.4f}%"
    print(f"平均绝对百分比误差     {lstm_mape_str:<25} {kalman_mape_str:<25} {mape_improvement:.2f}%")

    # NRMSE
    nrmse_improvement = (lstm_metrics['nrmse'] - kalman_metrics['nrmse']) / lstm_metrics['nrmse'] * 100
    lstm_nrmse_str = f"{lstm_metrics['nrmse']:.4f}%"
    kalman_nrmse_str = f"{kalman_metrics['nrmse']:.4f}%"
    print(f"标准化RMSE             {lstm_nrmse_str:<25} {kalman_nrmse_str:<25} {nrmse_improvement:.2f}%")

    print(f"\n2. 相关性指标:")
    print("指标                   LSTM预测                    集成LSTM-卡尔曼                改善程度")
    print("-" * 90)

    # R²
    r2_improvement = kalman_metrics['r2_score'] - lstm_metrics['r2_score']
    lstm_r2_str = f"{lstm_metrics['r2_score']:.6f}"
    kalman_r2_str = f"{kalman_metrics['r2_score']:.6f}"
    print(f"决定系数(R²)           {lstm_r2_str:<25} {kalman_r2_str:<25} {r2_improvement:.6f}")

    # 相关系数
    corr_improvement = kalman_metrics['correlation'] - lstm_metrics['correlation']
    lstm_corr_str = f"{lstm_metrics['correlation']:.6f}"
    kalman_corr_str = f"{kalman_metrics['correlation']:.6f}"
    print(f"相关系数               {lstm_corr_str:<25} {kalman_corr_str:<25} {corr_improvement:.6f}")

    print(f"\n3. 准确率指标 (基于相对误差阈值):")
    print("误差阈值               LSTM预测                    集成LSTM-卡尔曼                改善程度")
    print("-" * 90)

    # 1%准确率
    acc1_improvement = kalman_metrics['accuracy_1percent'] - lstm_metrics['accuracy_1percent']
    lstm_acc1_str = f"{lstm_metrics['accuracy_1percent']:.2f}%"
    kalman_acc1_str = f"{kalman_metrics['accuracy_1percent']:.2f}%"
    print(f"±1%以内准确率          {lstm_acc1_str:<25} {kalman_acc1_str:<25} {acc1_improvement:.2f}%")

    # 5%准确率
    acc5_improvement = kalman_metrics['accuracy_5percent'] - lstm_metrics['accuracy_5percent']
    lstm_acc5_str = f"{lstm_metrics['accuracy_5percent']:.2f}%"
    kalman_acc5_str = f"{kalman_metrics['accuracy_5percent']:.2f}%"
    print(f"±5%以内准确率          {lstm_acc5_str:<25} {kalman_acc5_str:<25} {acc5_improvement:.2f}%")

    # 10%准确率
    acc10_improvement = kalman_metrics['accuracy_10percent'] - lstm_metrics['accuracy_10percent']
    lstm_acc10_str = f"{lstm_metrics['accuracy_10percent']:.2f}%"
    kalman_acc10_str = f"{kalman_metrics['accuracy_10percent']:.2f}%"
    print(f"±10%以内准确率         {lstm_acc10_str:<25} {kalman_acc10_str:<25} {acc10_improvement:.2f}%")

    print(f"\n4. 其他指标:")
    print("指标                   LSTM预测                    集成LSTM-卡尔曼                改善程度")
    print("-" * 90)

    # 最大误差
    max_error_improvement = (lstm_metrics['max_error'] - kalman_metrics['max_error']) / lstm_metrics['max_error'] * 100
    lstm_max_str = f"{lstm_metrics['max_error']:.2e}"
    kalman_max_str = f"{kalman_metrics['max_error']:.2e}"
    print(f"最大误差               {lstm_max_str:<25} {kalman_max_str:<25} {max_error_improvement:.2f}%")

    # 偏差
    lstm_bias_str = f"{lstm_metrics['bias']:.2e}"
    kalman_bias_str = f"{kalman_metrics['bias']:.2e}"
    print(f"平均偏差               {lstm_bias_str:<25} {kalman_bias_str:<25} N/A")

def visualize_accuracy_analysis(data):
    """可视化准确率分析"""
    # 提取数据
    real_values = data['real_displacement'].values
    lstm_predictions = data['lstm_prediction'].values
    kalman_predictions = data['improved_integrated_kalman_prediction'].values
    
    # 计算误差
    lstm_errors = np.abs(real_values - lstm_predictions)
    kalman_errors = np.abs(real_values - kalman_predictions)
    
    # 计算相对误差（百分比）
    lstm_relative_errors = np.abs((real_values - lstm_predictions) / real_values) * 100
    kalman_relative_errors = np.abs((real_values - kalman_predictions) / real_values) * 100
    
    # 创建图形
    fig = plt.figure(figsize=(16, 12))
    
    # 第一个子图：绝对误差对比
    plt.subplot(2, 3, 1)
    plt.plot(lstm_errors * 1e6, 'r-', alpha=0.7, linewidth=1, label='LSTM预测误差')
    plt.plot(kalman_errors * 1e6, 'g-', alpha=0.8, linewidth=1, label='集成LSTM-卡尔曼误差')
    plt.xlabel('时间步')
    plt.ylabel('绝对误差 (μm)')
    plt.title('绝对误差对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 第二个子图：相对误差对比
    plt.subplot(2, 3, 2)
    plt.plot(lstm_relative_errors, 'r-', alpha=0.7, linewidth=1, label='LSTM相对误差')
    plt.plot(kalman_relative_errors, 'g-', alpha=0.8, linewidth=1, label='集成LSTM-卡尔曼相对误差')
    plt.xlabel('时间步')
    plt.ylabel('相对误差 (%)')
    plt.title('相对误差对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.ylim(0, min(50, np.percentile(lstm_relative_errors, 95)))  # 限制y轴范围
    
    # 第三个子图：误差分布直方图
    plt.subplot(2, 3, 3)
    plt.hist(lstm_errors * 1e6, bins=50, alpha=0.6, color='red', label='LSTM误差分布', density=True)
    plt.hist(kalman_errors * 1e6, bins=50, alpha=0.6, color='green', label='集成方法误差分布', density=True)
    plt.xlabel('绝对误差 (μm)')
    plt.ylabel('密度')
    plt.title('误差分布直方图')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 第四个子图：散点图 - LSTM
    plt.subplot(2, 3, 4)
    plt.scatter(real_values * 1e6, lstm_predictions * 1e6, alpha=0.5, s=1, color='red')
    plt.plot([real_values.min() * 1e6, real_values.max() * 1e6], 
             [real_values.min() * 1e6, real_values.max() * 1e6], 'k--', alpha=0.8)
    plt.xlabel('真实值 (μm)')
    plt.ylabel('LSTM预测值 (μm)')
    plt.title('LSTM预测散点图')
    plt.grid(True, alpha=0.3)
    
    # 第五个子图：散点图 - 集成方法
    plt.subplot(2, 3, 5)
    plt.scatter(real_values * 1e6, kalman_predictions * 1e6, alpha=0.5, s=1, color='green')
    plt.plot([real_values.min() * 1e6, real_values.max() * 1e6], 
             [real_values.min() * 1e6, real_values.max() * 1e6], 'k--', alpha=0.8)
    plt.xlabel('真实值 (μm)')
    plt.ylabel('集成预测值 (μm)')
    plt.title('集成LSTM-卡尔曼预测散点图')
    plt.grid(True, alpha=0.3)
    
    # 第六个子图：累积准确率
    plt.subplot(2, 3, 6)
    thresholds = np.linspace(0, 20, 100)  # 0-20%的误差阈值
    lstm_cumulative_accuracy = []
    kalman_cumulative_accuracy = []
    
    for threshold in thresholds:
        lstm_acc = np.sum(lstm_relative_errors <= threshold) / len(lstm_relative_errors) * 100
        kalman_acc = np.sum(kalman_relative_errors <= threshold) / len(kalman_relative_errors) * 100
        lstm_cumulative_accuracy.append(lstm_acc)
        kalman_cumulative_accuracy.append(kalman_acc)
    
    plt.plot(thresholds, lstm_cumulative_accuracy, 'r-', linewidth=2, label='LSTM累积准确率')
    plt.plot(thresholds, kalman_cumulative_accuracy, 'g-', linewidth=2, label='集成方法累积准确率')
    plt.xlabel('误差阈值 (%)')
    plt.ylabel('累积准确率 (%)')
    plt.title('累积准确率曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 20)
    plt.ylim(0, 100)
    
    plt.tight_layout()
    plt.savefig('vib1_20k_accuracy_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("=== vib1.txt前20000个点预测准确率详细分析 ===")
    
    # 加载预测结果
    data = load_prediction_results()
    if data is None:
        return
    
    # 提取数据
    real_values = data['real_displacement'].values
    lstm_predictions = data['lstm_prediction'].values
    kalman_predictions = data['improved_integrated_kalman_prediction'].values
    
    # 计算准确率指标
    print("\n正在计算准确率指标...")
    lstm_metrics = calculate_accuracy_metrics(real_values, lstm_predictions, "LSTM预测")
    kalman_metrics = calculate_accuracy_metrics(real_values, kalman_predictions, "集成LSTM-卡尔曼")
    
    # 打印详细报告
    print_accuracy_report(lstm_metrics, kalman_metrics)
    
    # 可视化分析
    print("\n正在生成准确率可视化分析...")
    visualize_accuracy_analysis(data)
    
    print(f"\n准确率分析完成！")
    print(f"可视化结果已保存到: vib1_20k_accuracy_analysis.png")
    
    # 保存指标到文件
    metrics_summary = pd.DataFrame([lstm_metrics, kalman_metrics])
    metrics_summary.to_csv('vib1_20k_accuracy_metrics.csv', index=False)
    print(f"详细指标已保存到: vib1_20k_accuracy_metrics.csv")

if __name__ == "__main__":
    main()
