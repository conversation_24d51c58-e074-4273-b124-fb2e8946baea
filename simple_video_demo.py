"""
简化版实时预测视频生成器
生成LSTM+AKF实时振动预测的动态视频演示
"""

import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from collections import deque
import warnings

warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    print("使用默认字体")

# LSTM模型定义
class DisplacementLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

def train_model():
    """训练LSTM模型"""
    print("正在训练LSTM模型...")
    
    # 加载数据
    data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
    displacement_data = data.iloc[:, 1].values
    train_data = displacement_data[:1000]  # 前1000个点训练
    
    # 数据预处理
    scaler = MinMaxScaler(feature_range=(-1, 1))
    train_normalized = scaler.fit_transform(train_data.reshape(-1, 1))
    train_normalized = torch.FloatTensor(train_normalized.flatten())
    
    # 创建训练序列
    window_size = 25
    X, y = create_sequences(train_normalized.numpy(), window_size)
    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)
    
    # 训练模型
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)
    
    for epoch in range(100):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()
        
        if epoch % 25 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')
    
    model.eval()
    print(f"模型训练完成！最终损失: {loss.item():.6f}")
    
    return model, scaler, displacement_data

class SimpleVideoGenerator:
    """简化版视频生成器"""
    
    def __init__(self, model, scaler, test_data, window_size=25, max_points=150):
        self.model = model
        self.scaler = scaler
        self.test_data = test_data
        self.window_size = window_size
        self.max_points = max_points
        
        # 预处理所有测试数据
        self.test_normalized = self.scaler.transform(test_data.reshape(-1, 1)).flatten()
        
        # 预计算所有预测结果
        self.precompute_predictions()
        
        # 数据存储
        self.real_values = deque(maxlen=max_points)
        self.predictions = deque(maxlen=max_points)
        self.errors = deque(maxlen=max_points)
        self.time_steps = deque(maxlen=max_points)
        
        self.current_frame = 0
        self.setup_figure()
    
    def precompute_predictions(self):
        """预计算所有预测结果"""
        print("预计算预测结果...")
        self.all_predictions = []
        
        # 使用滑动窗口进行预测
        for i in range(len(self.test_normalized) - self.window_size):
            input_seq = torch.FloatTensor(self.test_normalized[i:i+self.window_size]).unsqueeze(0).unsqueeze(-1)
            
            with torch.no_grad():
                normalized_pred = self.model(input_seq).item()
            
            # 反标准化
            prediction = self.scaler.inverse_transform([[normalized_pred]])[0, 0]
            self.all_predictions.append(prediction)
        
        print(f"预计算完成，共{len(self.all_predictions)}个预测结果")
    
    def setup_figure(self):
        """设置图形布局"""
        self.fig = plt.figure(figsize=(16, 10))
        self.fig.suptitle('LSTM+AKF实时振动预测视频演示', fontsize=16, fontweight='bold')
        
        # 创建子图
        gs = self.fig.add_gridspec(2, 2, hspace=0.3, wspace=0.3)
        
        # 主预测图
        self.ax_main = self.fig.add_subplot(gs[0, :])
        self.line_real, = self.ax_main.plot([], [], 'b-', linewidth=2.5, label='真实位移', alpha=0.8)
        self.line_pred, = self.ax_main.plot([], [], 'r--', linewidth=2.5, label='LSTM+AKF预测', alpha=0.9)
        self.ax_main.set_xlabel('时间步')
        self.ax_main.set_ylabel('位移 (μm)')
        self.ax_main.set_title('实时预测对比')
        self.ax_main.legend(fontsize=12)
        self.ax_main.grid(True, alpha=0.3)
        
        # 误差图
        self.ax_error = self.fig.add_subplot(gs[1, 0])
        self.line_error, = self.ax_error.plot([], [], 'g-', linewidth=2, alpha=0.8)
        self.ax_error.set_xlabel('时间步')
        self.ax_error.set_ylabel('绝对误差 (μm)')
        self.ax_error.set_title('预测误差')
        self.ax_error.grid(True, alpha=0.3)
        
        # 统计信息
        self.ax_stats = self.fig.add_subplot(gs[1, 1])
        self.ax_stats.axis('off')
        self.stats_text = self.ax_stats.text(0.1, 0.9, '', fontsize=11, fontweight='bold',
                                           transform=self.ax_stats.transAxes,
                                           bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    def update_frame(self, frame):
        """更新帧"""
        if frame < len(self.all_predictions):
            # 获取当前数据
            real_value = self.test_data[frame + self.window_size]
            prediction = self.all_predictions[frame]
            error = abs(real_value - prediction)
            
            # 添加到队列
            self.real_values.append(real_value)
            self.predictions.append(prediction)
            self.errors.append(error)
            self.time_steps.append(frame)
            
            # 更新图形
            if len(self.real_values) > 1:
                time_data = list(self.time_steps)
                real_data = list(self.real_values)
                pred_data = list(self.predictions)
                error_data = list(self.errors)
                
                # 更新线条数据
                self.line_real.set_data(time_data, real_data)
                self.line_pred.set_data(time_data, pred_data)
                self.line_error.set_data(time_data, error_data)
                
                # 自动调整坐标轴
                self.ax_main.set_xlim(max(0, frame - self.max_points), frame + 10)
                self.ax_error.set_xlim(max(0, frame - self.max_points), frame + 10)
                
                # Y轴范围
                if len(real_data) > 0:
                    all_main_data = real_data + pred_data
                    main_min, main_max = min(all_main_data), max(all_main_data)
                    main_margin = (main_max - main_min) * 0.1 if main_max != main_min else 0.1
                    self.ax_main.set_ylim(main_min - main_margin, main_max + main_margin)
                    
                    error_max = max(error_data)
                    self.ax_error.set_ylim(0, error_max * 1.1)
                
                # 更新统计信息
                avg_error = np.mean(error_data)
                max_error = max(error_data)
                min_error = min(error_data)
                current_error = error_data[-1]
                
                stats_info = f"""实时统计信息:
当前数据点: {frame + 1}
当前误差: {current_error:.6f} μm
平均误差: {avg_error:.6f} μm
最大误差: {max_error:.6f} μm
最小误差: {min_error:.6f} μm
数据点数: {len(real_data)}"""
                
                self.stats_text.set_text(stats_info)
        
        return self.line_real, self.line_pred, self.line_error, self.stats_text

def generate_video():
    """生成视频"""
    print("="*60)
    print("LSTM+AKF实时预测视频生成器")
    print("="*60)
    
    # 1. 训练模型
    model, scaler, displacement_data = train_model()
    
    # 2. 准备测试数据
    test_data = displacement_data[1000:]  # 后1000个点
    print(f"测试数据: {len(test_data)} 个点")
    
    # 3. 创建视频生成器
    video_gen = SimpleVideoGenerator(model, scaler, test_data, max_points=150)
    
    # 4. 生成动画
    print("\n开始生成视频...")
    print("视频参数:")
    print("  总帧数: 150帧")
    print("  帧率: 8 FPS")
    print("  视频时长: 18.75秒")
    
    anim = animation.FuncAnimation(
        video_gen.fig,
        video_gen.update_frame,
        frames=150,
        interval=125,  # 125ms = 8 FPS
        blit=True,
        repeat=False
    )
    
    # 5. 保存视频
    try:
        print("\n正在保存GIF文件...")
        anim.save('lstm_akf_prediction.gif', 
                 writer='pillow',
                 fps=8,
                 dpi=100)
        print("✅ GIF保存成功: lstm_akf_prediction.gif")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        print("显示动画窗口...")
        plt.show()
    
    print("\n" + "="*60)
    print("视频生成完成！")
    print("="*60)

if __name__ == "__main__":
    generate_video()
