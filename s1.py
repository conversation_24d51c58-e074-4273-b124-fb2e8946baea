"""
可视化vib1.txt中前1000个点的振动位移数据
包含时域分析、频域分析和统计特性分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.fft import fft, fftfreq
import warnings

# 忽略警告
warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    print("使用默认字体")

def load_vib_data(filename='v1.txt', num_points=None):
    """
    加载v1.txt文件中的振动数据

    参数:
    filename: 数据文件名，默认为'v1.txt'
    num_points: 要加载的数据点数，None表示加载所有点

    返回:
    time_data: 时间数据（秒）
    displacement_data: 位移数据（微米）
    """
    try:
        # 读取v1.txt文件
        data = pd.read_csv(filename, sep='\t', encoding='utf-8')
        print(f"成功加载{filename}文件，总数据点数: {len(data)}")

        # 获取时间和位移列
        time_column = data.columns[0]  # 时间[ms]
        displacement_column = data.columns[1]  # 1-5-a1-(位移)-时域[μm]

        print(f"时间列名: {time_column}")
        print(f"位移列名: {displacement_column}")

        # 确定要使用的点数
        if num_points is None:
            num_points = len(data)
            print(f"使用所有 {num_points} 个数据点")
        else:
            if num_points > len(data):
                num_points = len(data)
                print(f"请求的点数超过文件总点数，使用所有 {num_points} 个点")
            else:
                print(f"使用前 {num_points} 个数据点")

        # 提取数据
        time_data = data[time_column].values[:num_points]
        displacement_data = data[displacement_column].values[:num_points]

        # 时间单位转换：毫秒转秒
        time_data = time_data / 1000.0

        # 位移单位保持微米（不转换为米）
        # displacement_data 保持原始微米单位
        
        print(f"数据加载成功:")
        print(f"  实际使用数据点数: {len(displacement_data)}")
        print(f"  时间范围: {np.min(time_data):.6f} ~ {np.max(time_data):.6f} 秒")
        print(f"  位移范围: {np.min(displacement_data):.6f} ~ {np.max(displacement_data):.6f} μm")
        print(f"  位移标准差: {np.std(displacement_data):.6f} μm")
        print(f"  峰峰值: {np.max(displacement_data) - np.min(displacement_data):.6f} μm")
        
        return time_data, displacement_data
        
    except FileNotFoundError:
        print(f"错误: 未找到 {filename} 文件")
        return None, None
    except Exception as e:
        print(f"读取{filename}文件时出错: {e}")
        return None, None

def calculate_statistics(displacement_data):
    """计算位移数据的统计特性"""
    stats = {
        'mean': np.mean(displacement_data),
        'std': np.std(displacement_data),
        'min': np.min(displacement_data),
        'max': np.max(displacement_data),
        'peak_to_peak': np.max(displacement_data) - np.min(displacement_data),
        'rms': np.sqrt(np.mean(displacement_data**2)),
        'variance': np.var(displacement_data)
    }
    return stats

def perform_fft_analysis(time_data, displacement_data):
    """进行FFT频域分析"""
    # 计算采样频率
    dt = time_data[1] - time_data[0]  # 采样间隔
    fs = 1.0 / dt  # 采样频率
    
    # 进行FFT
    N = len(displacement_data)
    fft_values = fft(displacement_data)
    frequencies = fftfreq(N, dt)
    
    # 只取正频率部分
    positive_freq_idx = frequencies > 0
    frequencies_positive = frequencies[positive_freq_idx]
    fft_magnitude = np.abs(fft_values[positive_freq_idx])
    
    # 找到主要频率成分
    peak_indices = signal.find_peaks(fft_magnitude, height=np.max(fft_magnitude)*0.1)[0]
    main_frequencies = frequencies_positive[peak_indices]
    main_amplitudes = fft_magnitude[peak_indices]
    
    print(f"FFT分析结果:")
    print(f"  采样频率: {fs:.2f} Hz")
    print(f"  频率分辨率: {frequencies_positive[1]:.4f} Hz")
    print(f"  主要频率成分:")
    for i, (freq, amp) in enumerate(zip(main_frequencies[:5], main_amplitudes[:5])):
        print(f"    频率 {i+1}: {freq:.2f} Hz, 幅值: {amp:.2e}")
    
    return frequencies_positive, fft_magnitude, fs

def visualize_vibration_data(time_data, displacement_data):
    """可视化振动数据"""
    # 计算统计特性
    stats = calculate_statistics(displacement_data)
    
    # 进行FFT分析
    frequencies, fft_magnitude, fs = perform_fft_analysis(time_data, displacement_data)
    
    # 创建图形
    fig = plt.figure(figsize=(16, 12))
    
    # 第一个子图：时域波形
    plt.subplot(2, 2, 1)
    plt.plot(time_data, displacement_data, 'b-', linewidth=1, alpha=0.8)
    plt.xlabel('时间 (秒)')
    plt.ylabel('位移 (μm)')
    plt.title(f'振动位移时域波形 (v1.txt - {len(displacement_data)}个点)')
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    stats_text = f'统计特性:\n均值: {stats["mean"]:.4f} μm\n标准差: {stats["std"]:.4f} μm\n峰峰值: {stats["peak_to_peak"]:.4f} μm\nRMS: {stats["rms"]:.4f} μm'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
             fontsize=9)
    
    # 第二个子图：频域分析
    plt.subplot(2, 2, 2)
    plt.semilogy(frequencies, fft_magnitude, 'r-', linewidth=1, alpha=0.8)
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值')
    plt.title('振动位移频域分析 (FFT)')
    plt.grid(True, alpha=0.3)
    plt.xlim(0, min(100, fs/2))  # 显示0-100Hz或奈奎斯特频率
    
    # 第三个子图：位移分布直方图
    plt.subplot(2, 2, 3)
    plt.hist(displacement_data, bins=50, alpha=0.7, color='green', edgecolor='black')
    plt.xlabel('位移 (μm)')
    plt.ylabel('频次')
    plt.title('位移分布直方图')
    plt.grid(True, alpha=0.3)

    # 添加正态分布拟合
    mu, sigma = stats['mean'], stats['std']
    x = np.linspace(np.min(displacement_data), np.max(displacement_data), 100)
    y = len(displacement_data) * (np.max(displacement_data) - np.min(displacement_data)) / 50 * \
        np.exp(-0.5 * ((x - mu) / sigma) ** 2) / (sigma * np.sqrt(2 * np.pi))
    plt.plot(x, y, 'r--', linewidth=2, label=f'正态分布拟合\nμ={mu:.4f}, σ={sigma:.4f}')
    plt.legend()
    
    # 第四个子图：时频分析（短时傅里叶变换）
    plt.subplot(2, 2, 4)
    f, t, Sxx = signal.spectrogram(displacement_data, fs, nperseg=min(256, len(displacement_data)//4))
    plt.pcolormesh(t, f, 10 * np.log10(Sxx), shading='gouraud', cmap='viridis')
    plt.ylabel('频率 (Hz)')
    plt.xlabel('时间 (秒)')
    plt.title('时频分析 (短时傅里叶变换)')
    plt.colorbar(label='功率谱密度 (dB)')
    plt.ylim(0, min(50, fs/2))  # 显示0-50Hz或奈奎斯特频率
    
    plt.tight_layout()
    plt.savefig('v1_visualization_2000points.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return stats, frequencies, fft_magnitude

def print_detailed_analysis(stats, frequencies, fft_magnitude):
    """打印详细分析结果"""
    print("\n" + "="*60)
    print("详细分析结果")
    print("="*60)
    
    print(f"\n时域统计特性:")
    print(f"  均值: {stats['mean']:.6f} μm")
    print(f"  标准差: {stats['std']:.6f} μm")
    print(f"  最小值: {stats['min']:.6f} μm")
    print(f"  最大值: {stats['max']:.6f} μm")
    print(f"  峰峰值: {stats['peak_to_peak']:.6f} μm")
    print(f"  RMS值: {stats['rms']:.6f} μm")
    print(f"  方差: {stats['variance']:.6f} μm²")
    
    # 找到主要频率成分
    peak_indices = signal.find_peaks(fft_magnitude, height=np.max(fft_magnitude)*0.05)[0]
    main_frequencies = frequencies[peak_indices]
    main_amplitudes = fft_magnitude[peak_indices]
    
    # 按幅值排序
    sorted_indices = np.argsort(main_amplitudes)[::-1]
    
    print(f"\n频域主要成分 (前10个):")
    for i, idx in enumerate(sorted_indices[:10]):
        freq = main_frequencies[idx]
        amp = main_amplitudes[idx]
        print(f"  {i+1:2d}. 频率: {freq:8.2f} Hz, 幅值: {amp:.2e}, 相对幅值: {amp/np.max(fft_magnitude)*100:.1f}%")

def main():
    """主函数"""
    print("=== v1.txt振动数据可视化分析 ===")
    print()

    # 加载数据（加载所有2000个点）
    time_data, displacement_data = load_vib_data('v1.txt', None)

    if time_data is None or displacement_data is None:
        print("数据加载失败，程序退出")
        return

    print()

    # 可视化分析
    print("开始可视化分析...")
    stats, frequencies, fft_magnitude = visualize_vibration_data(time_data, displacement_data)

    # 打印详细分析结果
    print_detailed_analysis(stats, frequencies, fft_magnitude)

    print(f"\n可视化结果已保存到: v1_visualization_2000points.png")
    print("分析完成！")

if __name__ == "__main__":
    main()
