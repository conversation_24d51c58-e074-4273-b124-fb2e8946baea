"""
实时数据流处理可视化演示
生成静态图表展示实时处理效果
"""

import asyncio
import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
from real_time_stream_processor import RealTimeStreamProcessor, SimulatedDataSource
import warnings
from collections import deque

warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    print("使用默认字体")

# 简化的LSTM模型
class DisplacementLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []

    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)

    return np.array(sequences), np.array(targets)

# 自适应卡尔曼滤波器（参考improve.py）
class AdaptiveKalmanFilter:
    """自适应卡尔曼滤波器，用于LSTM预测结果的后处理优化"""

    def __init__(self, model, scaler, window_size=25,
                 initial_process_variance=1e-4, initial_measurement_variance=5e-4):
        self.model = model
        self.scaler = scaler
        self.window_size = window_size
        self.data_buffer = deque(maxlen=window_size)

        # 卡尔曼滤波参数（2维状态：位移和速度）
        self.B = 0  # 外部输入为0
        self.u = 0  # 外部输入为0

        # 状态向量 [位移, 速度]
        self.x = np.array([0.0, 0.0])

        # 状态转移矩阵A (2x2)
        dt = 1.0  # 时间步长
        self.A = np.array([[1.0, dt],
                          [0.0, 1.0]])

        # 过程噪声协方差矩阵Q (2x2)
        self.Q = np.diag([initial_process_variance, initial_process_variance * 2.0])

        # 观测矩阵H (1x2) - 只观测位移
        self.H = np.array([[1.0, 0.0]])

        # 观测噪声协方差矩阵R (1x1)
        self.R = np.array([[initial_measurement_variance]])

        # 估计误差协方差矩阵P (2x2)
        self.P = np.diag([1.0, 1.0])

        # 自适应参数
        self.measurement_history = []
        self.innovation_history = []
        self.max_history = 15
        self.displacement_threshold = 2e-3
        self.adaptation_rate = 0.08

        # LSTM预测存储
        self.lstm_prediction = None

    def initialize_buffer(self, initial_data):
        """初始化LSTM数据缓冲区"""
        if len(initial_data) < self.window_size:
            raise ValueError(f"初始数据长度必须至少为 {self.window_size}")

        # 标准化初始数据
        normalized_data = self.scaler.transform(
            np.array(initial_data[-self.window_size:]).reshape(-1, 1)
        ).flatten()

        # 填充缓冲区
        self.data_buffer.clear()
        for value in normalized_data:
            self.data_buffer.append(value)

        # 初始化状态
        self.x[0] = initial_data[-1]  # 初始位移
        self.x[1] = 0.0  # 初始速度

    def predict_and_update(self, observation=None):
        """集成LSTM预测的卡尔曼滤波预测和更新步骤"""
        # 1. 传统卡尔曼预测步骤
        x_pred = np.dot(self.A, self.x) + self.B * self.u

        # 2. LSTM预测步骤
        lstm_displacement_pred = None
        if len(self.data_buffer) >= self.window_size:
            # 准备LSTM输入序列
            input_seq = torch.FloatTensor(list(self.data_buffer)).unsqueeze(0).unsqueeze(-1)

            # LSTM预测
            with torch.no_grad():
                normalized_prediction = self.model(input_seq).item()

            # 反标准化得到位移预测
            lstm_displacement_pred = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]

            # 动态融合策略：根据信号变化程度调整权重
            if len(self.measurement_history) >= 2:
                recent_change = abs(self.measurement_history[-1] - self.measurement_history[-2])
                if recent_change > self.displacement_threshold * 2:
                    lstm_weight = 0.8  # 快速变化时增加LSTM权重
                elif recent_change > self.displacement_threshold:
                    lstm_weight = 0.75  # 中等变化时适中权重
                else:
                    lstm_weight = 0.7  # 平缓变化时标准权重
            else:
                lstm_weight = 0.7  # 默认权重

            kalman_weight = 1.0 - lstm_weight

            # 融合预测：结合LSTM预测和卡尔曼物理模型预测
            x_pred[0] = lstm_weight * lstm_displacement_pred + kalman_weight * x_pred[0]

            # 更新数据缓冲区
            if observation is not None:
                normalized_obs = self.scaler.transform([[observation]])[0, 0]
                self.data_buffer.append(normalized_obs)

        # 保存LSTM预测用于对比
        self.lstm_prediction = lstm_displacement_pred

        # 3. 预测误差协方差更新
        P_pred = np.dot(np.dot(self.A, self.P), self.A.T) + self.Q

        # 4. 如果有观测值，进行更新步骤
        if observation is not None:
            self.z = np.array([observation])

            # 记录测量历史用于自适应调整
            self.measurement_history.append(observation)
            if len(self.measurement_history) > self.max_history:
                self.measurement_history.pop(0)

            # 计算卡尔曼增益
            S = np.dot(np.dot(self.H, P_pred), self.H.T) + self.R
            K = np.dot(np.dot(P_pred, self.H.T), np.linalg.inv(S))

            # 计算创新
            innovation = self.z - np.dot(self.H, x_pred)
            self.innovation_history.append(innovation[0])
            if len(self.innovation_history) > self.max_history:
                self.innovation_history.pop(0)

            # 自适应调整
            self._adaptive_adjustment()

            # 状态更新
            self.x = x_pred + np.dot(K, innovation)

            # 协方差更新
            self.P = P_pred - np.dot(np.dot(K, self.H), P_pred)
        else:
            # 没有观测值时，只进行预测
            self.x = x_pred
            self.P = P_pred

        return self.x[0]  # 返回位移估计值

    def _adaptive_adjustment(self):
        """自适应调整噪声参数"""
        if len(self.measurement_history) < 3:
            return

        recent_measurements = np.array(self.measurement_history[-3:])
        displacement_changes = np.abs(np.diff(recent_measurements))
        avg_change = np.mean(displacement_changes)
        max_change = np.max(displacement_changes)

        # 根据变化程度调整过程噪声
        if max_change > self.displacement_threshold * 3:
            self.Q[0, 0] = min(1e-2, self.Q[0, 0] * 1.5)
            self.Q[1, 1] = min(2e-2, self.Q[1, 1] * 1.8)
        elif max_change > self.displacement_threshold * 2:
            self.Q[0, 0] = min(5e-3, self.Q[0, 0] * 1.2)
            self.Q[1, 1] = min(1e-2, self.Q[1, 1] * 1.3)
        elif avg_change > self.displacement_threshold:
            self.Q[0, 0] = min(2e-3, self.Q[0, 0] * 1.05)
            self.Q[1, 1] = min(5e-3, self.Q[1, 1] * 1.1)
        else:
            self.Q[0, 0] = max(1e-6, self.Q[0, 0] * 0.99)
            self.Q[1, 1] = max(1e-5, self.Q[1, 1] * 0.98)

        # 基于创新序列调整测量噪声
        if len(self.innovation_history) >= 3:
            innovation_var = np.var(self.innovation_history[-3:])
            if innovation_var > 0:
                target_R = innovation_var * 0.6
                self.R[0, 0] = (1 - self.adaptation_rate) * self.R[0, 0] + self.adaptation_rate * target_R
                self.R[0, 0] = np.clip(self.R[0, 0], 1e-6, 1e-2)



def train_model_for_visualization():
    """训练模型用于可视化演示"""
    print("正在训练LSTM模型...")

    # 1. 加载训练数据
    try:
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values  # 位移列
        print(f"加载v1.txt数据成功，总数据点数: {len(displacement_data)}")

        # 使用前1000个点作为训练数据
        train_data = displacement_data[:1000]
        print(f"使用前1000个点进行模型训练")

    except FileNotFoundError:
        print("错误: 未找到v1.txt文件")
        return None, None

    # 2. 数据预处理（只用训练数据拟合scaler）
    scaler = MinMaxScaler(feature_range=(-1, 1))
    train_normalized = scaler.fit_transform(train_data.reshape(-1, 1))
    train_normalized = torch.FloatTensor(train_normalized.flatten())
    
    # 3. 创建训练序列（使用训练数据）
    window_size = 25
    X, y = create_sequences(train_normalized.numpy(), window_size)

    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)

    print(f"训练序列数量: {len(X)}, 窗口大小: {window_size}")
    
    # 4. 训练模型
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)

    epochs = 100  # 增加训练轮次以提高精度
    print(f"开始训练，共{epochs}个epoch...")
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()

        if epoch % 25 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')

    model.eval()
    print(f"模型训练完成！最终损失: {loss.item():.6f}")

    return model, scaler

async def collect_real_time_data(processor, data_source, num_points=200):
    """收集实时处理数据"""
    print(f"开始收集 {num_points} 个实时处理数据点...")

    real_values = []
    predictions = []
    timestamps = []
    processing_times = []

    successful_predictions = 0

    for i in range(num_points):
        # 模拟实时数据读取
        raw_data = await data_source.read_data()

        if raw_data is not None:
            start_time = time.time()
            prediction = processor.process_single_point(raw_data)
            processing_time = time.time() - start_time

            if prediction is not None:
                real_values.append(raw_data)
                predictions.append(prediction)
                timestamps.append(time.time())
                processing_times.append(processing_time)
                successful_predictions += 1

        # 控制采集频率
        await asyncio.sleep(0.005)  # 200Hz采集，加快演示速度

        # 更频繁的进度报告
        if (i + 1) % 100 == 0:
            print(f"已处理 {i+1}/{num_points} 个数据点，成功预测 {successful_predictions} 个")

    print(f"数据收集完成！总共成功预测 {successful_predictions} 个数据点")

    return {
        'real_values': real_values,
        'predictions': predictions,
        'timestamps': timestamps,
        'processing_times': processing_times
    }

async def collect_comparison_data(lstm_processor, akf_processor, test_data):
    """收集LSTM vs LSTM+AKF对比数据"""
    real_values = []
    lstm_predictions = []
    akf_predictions = []
    processing_times = []

    print(f"开始收集{len(test_data)}个对比预测数据点...")

    for i, real_value in enumerate(test_data):
        start_time = time.time()

        # LSTM预测
        lstm_prediction = lstm_processor.process_single_point(real_value)

        # LSTM+AKF预测（正确的预测方式）
        # 1. 先进行预测（不给观测值）
        akf_prediction = akf_processor.predict_and_update(observation=None)
        # 2. 然后用真实值更新状态（为下一次预测做准备）
        akf_processor.predict_and_update(observation=real_value)

        processing_time = time.time() - start_time

        if lstm_prediction is not None and akf_prediction is not None:
            real_values.append(real_value)
            lstm_predictions.append(lstm_prediction)
            akf_predictions.append(akf_prediction)
            processing_times.append(processing_time)

        # 每100个点显示进度
        if (i + 1) % 100 == 0:
            print(f"已处理 {i+1}/{len(test_data)} 个数据点")

        # 控制处理频率
        await asyncio.sleep(0.001)

    print(f"对比数据收集完成，共收集到 {len(lstm_predictions)} 个有效预测")

    return {
        'real_values': real_values,
        'lstm_predictions': lstm_predictions,
        'akf_predictions': akf_predictions,
        'processing_times': processing_times
    }

def create_real_time_visualization(data):
    """创建实时处理可视化图表"""
    print("生成实时处理可视化图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('实时数据流处理可视化演示', fontsize=16, fontweight='bold')
    
    real_values = np.array(data['real_values'])
    predictions = np.array(data['predictions'])
    processing_times = np.array(data['processing_times'])
    time_steps = range(len(real_values))
    
    # 计算误差
    errors = np.abs(real_values - predictions)
    
    # 第一张图：实时预测对比
    axes[0, 0].plot(time_steps, real_values, 'b-', linewidth=2, label='真实位移', alpha=0.8)
    axes[0, 0].plot(time_steps, predictions, 'r--', linewidth=2, label='LSTM+AKF预测', alpha=0.8)
    axes[0, 0].set_xlabel('时间步')
    axes[0, 0].set_ylabel('位移 (μm)')
    axes[0, 0].set_title('实时预测对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加统计信息
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean(errors**2))
    # 计算10%准确率
    relative_errors = np.abs((real_values - predictions) / real_values) * 100
    accuracy_10percent = np.sum(relative_errors <= 10.0) / len(relative_errors) * 100

    axes[0, 0].text(0.02, 0.98, f'MAE: {mae:.6f}μm\nRMSE: {rmse:.6f}μm\n±10%准确率: {accuracy_10percent:.1f}%',
                    transform=axes[0, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 第二张图：预测误差
    axes[0, 1].plot(time_steps, errors, 'g-', linewidth=2, label='预测误差', alpha=0.8)
    axes[0, 1].axhline(y=np.mean(errors), color='orange', linestyle='--', 
                       label=f'平均误差: {np.mean(errors):.6f}μm')
    axes[0, 1].set_xlabel('时间步')
    axes[0, 1].set_ylabel('绝对误差 (μm)')
    axes[0, 1].set_title('实时预测误差')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 第三张图：处理时间分析
    axes[1, 0].plot(time_steps, processing_times * 1000, 'purple', linewidth=2, 
                    label='处理时间', alpha=0.8)
    axes[1, 0].axhline(y=np.mean(processing_times) * 1000, color='red', linestyle='--',
                       label=f'平均时间: {np.mean(processing_times)*1000:.2f}ms')
    axes[1, 0].set_xlabel('时间步')
    axes[1, 0].set_ylabel('处理时间 (ms)')
    axes[1, 0].set_title('实时处理性能')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 添加性能统计
    avg_time = np.mean(processing_times) * 1000
    max_time = np.max(processing_times) * 1000
    processing_rate = 1.0 / np.mean(processing_times)
    axes[1, 0].text(0.02, 0.98, f'平均: {avg_time:.2f}ms\n最大: {max_time:.2f}ms\n速率: {processing_rate:.1f}次/秒', 
                    transform=axes[1, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 第四张图：误差分布直方图
    axes[1, 1].hist(errors, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 1].axvline(x=np.mean(errors), color='red', linestyle='--', linewidth=2,
                       label=f'平均误差: {np.mean(errors):.6f}μm')
    axes[1, 1].axvline(x=np.median(errors), color='green', linestyle='--', linewidth=2,
                       label=f'中位数误差: {np.median(errors):.6f}μm')
    axes[1, 1].set_xlabel('预测误差 (μm)')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].set_title('误差分布直方图')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('real_time_processing_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("可视化图表已保存到: real_time_processing_visualization.png")

def create_performance_dashboard(data):
    """创建性能仪表板"""
    print("生成性能仪表板...")
    
    real_values = np.array(data['real_values'])
    predictions = np.array(data['predictions'])
    processing_times = np.array(data['processing_times'])
    
    # 计算关键指标
    errors = np.abs(real_values - predictions)
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean(errors**2))
    max_error = np.max(errors)
    min_error = np.min(errors)
    
    avg_processing_time = np.mean(processing_times) * 1000
    max_processing_time = np.max(processing_times) * 1000
    processing_rate = 1.0 / np.mean(processing_times)
    
    # 计算准确率
    relative_errors = np.abs((real_values - predictions) / real_values) * 100
    accuracy_1percent = np.sum(relative_errors <= 1.0) / len(relative_errors) * 100
    accuracy_5percent = np.sum(relative_errors <= 5.0) / len(relative_errors) * 100
    accuracy_10percent = np.sum(relative_errors <= 10.0) / len(relative_errors) * 100
    
    # 创建仪表板
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('实时数据流处理性能仪表板', fontsize=18, fontweight='bold')
    
    # 1. 精度指标
    metrics = ['MAE', 'RMSE', '最大误差', '最小误差']
    values = [mae, rmse, max_error, min_error]
    colors = ['skyblue', 'lightgreen', 'salmon', 'gold']
    
    bars = axes[0, 0].bar(metrics, values, color=colors, alpha=0.8)
    axes[0, 0].set_ylabel('误差 (μm)')
    axes[0, 0].set_title('预测精度指标')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(values)*0.01,
                        f'{value:.6f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. 性能指标
    perf_metrics = ['平均处理时间\n(ms)', '最大处理时间\n(ms)', '处理速率\n(次/秒)']
    perf_values = [avg_processing_time, max_processing_time, processing_rate]
    perf_colors = ['lightcoral', 'orange', 'lightblue']
    
    bars = axes[0, 1].bar(perf_metrics, perf_values, color=perf_colors, alpha=0.8)
    axes[0, 1].set_ylabel('数值')
    axes[0, 1].set_title('处理性能指标')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, perf_values):
        axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(perf_values)*0.01,
                        f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
    
    # 3. 准确率指标
    acc_metrics = ['±1%准确率', '±5%准确率', '±10%准确率\n(关键指标)']
    acc_values = [accuracy_1percent, accuracy_5percent, accuracy_10percent]
    acc_colors = ['mediumseagreen', 'darkseagreen', 'gold']  # 10%准确率用金色突出显示
    
    bars = axes[0, 2].bar(acc_metrics, acc_values, color=acc_colors, alpha=0.8)
    axes[0, 2].set_ylabel('准确率 (%)')
    axes[0, 2].set_title('相对准确率指标')
    axes[0, 2].set_ylim(0, 100)
    axes[0, 2].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, acc_values):
        axes[0, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2,
                        f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 4. 实时数据流
    time_steps = range(len(real_values))
    axes[1, 0].plot(time_steps, real_values, 'b-', linewidth=1.5, label='真实值', alpha=0.8)
    axes[1, 0].plot(time_steps, predictions, 'r--', linewidth=1.5, label='预测值', alpha=0.8)
    axes[1, 0].set_xlabel('时间步')
    axes[1, 0].set_ylabel('位移 (μm)')
    axes[1, 0].set_title('实时数据流')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 误差时间序列
    axes[1, 1].plot(time_steps, errors, 'g-', linewidth=2, alpha=0.8)
    axes[1, 1].axhline(y=mae, color='red', linestyle='--', label=f'平均误差: {mae:.6f}μm')
    axes[1, 1].set_xlabel('时间步')
    axes[1, 1].set_ylabel('绝对误差 (μm)')
    axes[1, 1].set_title('误差时间序列')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 处理时间分布
    axes[1, 2].hist(processing_times * 1000, bins=20, alpha=0.7, color='plum', edgecolor='black')
    axes[1, 2].axvline(x=avg_processing_time, color='red', linestyle='--', linewidth=2,
                       label=f'平均: {avg_processing_time:.2f}ms')
    axes[1, 2].set_xlabel('处理时间 (ms)')
    axes[1, 2].set_ylabel('频次')
    axes[1, 2].set_title('处理时间分布')
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('real_time_performance_dashboard.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("性能仪表板已保存到: real_time_performance_dashboard.png")

def create_comparison_visualization(data):
    """创建LSTM vs LSTM+AKF对比可视化图表"""
    print("生成LSTM vs LSTM+AKF对比可视化图表...")

    real_values = np.array(data['real_values'])
    lstm_predictions = np.array(data['lstm_predictions'])
    akf_predictions = np.array(data['akf_predictions'])

    # 创建时间步
    time_steps = range(len(real_values))

    # 计算误差
    lstm_errors = np.abs(real_values - lstm_predictions)
    akf_errors = np.abs(real_values - akf_predictions)

    # 创建2x2子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('LSTM vs LSTM+AKF 预测效果对比', fontsize=16, fontweight='bold')

    # 第一张图：预测对比
    axes[0, 0].plot(time_steps, real_values, 'b-', linewidth=2, label='真实位移', alpha=0.8)
    axes[0, 0].plot(time_steps, lstm_predictions, 'r--', linewidth=1.5, label='LSTM预测', alpha=0.7)
    axes[0, 0].plot(time_steps, akf_predictions, 'g:', linewidth=2, label='LSTM+AKF预测', alpha=0.8)
    axes[0, 0].set_xlabel('时间步')
    axes[0, 0].set_ylabel('位移 (μm)')
    axes[0, 0].set_title('预测效果对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 添加统计信息
    lstm_mae = np.mean(lstm_errors)
    akf_mae = np.mean(akf_errors)
    improvement = ((lstm_mae - akf_mae) / lstm_mae) * 100

    axes[0, 0].text(0.02, 0.98, f'LSTM MAE: {lstm_mae:.6f}μm\nLSTM+AKF MAE: {akf_mae:.6f}μm\n改进: {improvement:.2f}%',
                    transform=axes[0, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 第二张图：误差对比
    axes[0, 1].plot(time_steps, lstm_errors, 'r-', linewidth=1.5, label='LSTM误差', alpha=0.7)
    axes[0, 1].plot(time_steps, akf_errors, 'g-', linewidth=1.5, label='LSTM+AKF误差', alpha=0.7)
    axes[0, 1].set_xlabel('时间步')
    axes[0, 1].set_ylabel('绝对误差 (μm)')
    axes[0, 1].set_title('误差对比')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 第三张图：LSTM+AKF误差曲线（对数坐标）
    # 为了避免对数坐标的问题，将零值替换为极小值
    akf_errors_log = np.where(akf_errors == 0, 1e-10, akf_errors)
    axes[1, 0].plot(time_steps, akf_errors_log, 'g-', linewidth=1.5, label='LSTM+AKF误差', alpha=0.8)
    axes[1, 0].set_xlabel('时间步')
    axes[1, 0].set_ylabel('绝对误差 (μm) - 对数坐标')
    axes[1, 0].set_title('LSTM+AKF误差曲线 (对数坐标)')
    axes[1, 0].set_yscale('log')  # 设置y轴为对数坐标
    axes[1, 0].grid(True, alpha=0.3)

    # 添加统计信息
    akf_mae = np.mean(akf_errors)
    akf_median = np.median(akf_errors)
    akf_max = np.max(akf_errors)
    akf_min = np.min(akf_errors[akf_errors > 0]) if np.any(akf_errors > 0) else 1e-10

    axes[1, 0].axhline(y=akf_mae, color='red', linestyle='--', alpha=0.8,
                       label=f'平均误差: {akf_mae:.2e}μm')
    axes[1, 0].axhline(y=akf_median, color='orange', linestyle=':', alpha=0.8,
                       label=f'中位数误差: {akf_median:.2e}μm')
    axes[1, 0].text(0.02, 0.98, f'最大: {akf_max:.2e}μm\n最小: {akf_min:.2e}μm',
                    transform=axes[1, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    axes[1, 0].legend()

    # 第四张图：误差分布对比
    axes[1, 1].hist(lstm_errors, bins=30, alpha=0.6, color='red', label='LSTM误差', edgecolor='black')
    axes[1, 1].hist(akf_errors, bins=30, alpha=0.6, color='green', label='LSTM+AKF误差', edgecolor='black')
    axes[1, 1].set_xlabel('绝对误差 (μm)')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].set_title('误差分布对比')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('lstm_vs_akf_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("LSTM vs LSTM+AKF对比可视化已保存到: lstm_vs_akf_comparison.png")

def create_comparison_dashboard(data):
    """创建对比性能仪表板"""
    print("生成对比性能仪表板...")

    real_values = np.array(data['real_values'])
    lstm_predictions = np.array(data['lstm_predictions'])
    akf_predictions = np.array(data['akf_predictions'])

    # 计算各种指标
    lstm_errors = np.abs(real_values - lstm_predictions)
    akf_errors = np.abs(real_values - akf_predictions)

    lstm_mae = np.mean(lstm_errors)
    akf_mae = np.mean(akf_errors)
    lstm_rmse = np.sqrt(np.mean(lstm_errors**2))
    akf_rmse = np.sqrt(np.mean(akf_errors**2))

    # 计算准确率
    lstm_relative_errors = np.abs((real_values - lstm_predictions) / real_values) * 100
    akf_relative_errors = np.abs((real_values - akf_predictions) / real_values) * 100

    lstm_acc_1 = np.sum(lstm_relative_errors <= 1.0) / len(lstm_relative_errors) * 100
    lstm_acc_5 = np.sum(lstm_relative_errors <= 5.0) / len(lstm_relative_errors) * 100
    lstm_acc_10 = np.sum(lstm_relative_errors <= 10.0) / len(lstm_relative_errors) * 100

    akf_acc_1 = np.sum(akf_relative_errors <= 1.0) / len(akf_relative_errors) * 100
    akf_acc_5 = np.sum(akf_relative_errors <= 5.0) / len(akf_relative_errors) * 100
    akf_acc_10 = np.sum(akf_relative_errors <= 10.0) / len(akf_relative_errors) * 100

    # 创建仪表板
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('LSTM vs LSTM+AKF 性能对比仪表板', fontsize=16, fontweight='bold')

    # 1. MAE对比
    methods = ['LSTM', 'LSTM+AKF']
    mae_values = [lstm_mae, akf_mae]
    colors = ['red', 'green']
    bars1 = axes[0, 0].bar(methods, mae_values, color=colors, alpha=0.7)
    axes[0, 0].set_ylabel('平均绝对误差 (μm)')
    axes[0, 0].set_title('MAE对比')
    axes[0, 0].grid(True, alpha=0.3)

    # 添加数值标签
    for bar, value in zip(bars1, mae_values):
        axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.01,
                        f'{value:.6f}', ha='center', va='bottom')

    # 2. RMSE对比
    rmse_values = [lstm_rmse, akf_rmse]
    bars2 = axes[0, 1].bar(methods, rmse_values, color=colors, alpha=0.7)
    axes[0, 1].set_ylabel('均方根误差 (μm)')
    axes[0, 1].set_title('RMSE对比')
    axes[0, 1].grid(True, alpha=0.3)

    for bar, value in zip(bars2, rmse_values):
        axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.01,
                        f'{value:.6f}', ha='center', va='bottom')

    # 3. 10%准确率对比
    acc_10_values = [lstm_acc_10, akf_acc_10]
    bars3 = axes[0, 2].bar(methods, acc_10_values, color=colors, alpha=0.7)
    axes[0, 2].set_ylabel('10%准确率 (%)')
    axes[0, 2].set_title('10%阈值准确率对比')
    axes[0, 2].grid(True, alpha=0.3)

    for bar, value in zip(bars3, acc_10_values):
        axes[0, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                        f'{value:.1f}%', ha='center', va='bottom')

    # 4. 准确率对比雷达图
    categories = ['±1%准确率', '±5%准确率', '±10%准确率']
    lstm_acc_values = [lstm_acc_1, lstm_acc_5, lstm_acc_10]
    akf_acc_values = [akf_acc_1, akf_acc_5, akf_acc_10]

    x = np.arange(len(categories))
    width = 0.35

    bars4 = axes[1, 0].bar(x - width/2, lstm_acc_values, width, label='LSTM', color='red', alpha=0.7)
    bars5 = axes[1, 0].bar(x + width/2, akf_acc_values, width, label='LSTM+AKF', color='green', alpha=0.7)

    axes[1, 0].set_ylabel('准确率 (%)')
    axes[1, 0].set_title('准确率对比')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(categories)
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 5. 改进效果
    mae_improvement = ((lstm_mae - akf_mae) / lstm_mae) * 100
    rmse_improvement = ((lstm_rmse - akf_rmse) / lstm_rmse) * 100
    acc_10_improvement = akf_acc_10 - lstm_acc_10

    improvements = [mae_improvement, rmse_improvement, acc_10_improvement]
    improvement_labels = ['MAE改进(%)', 'RMSE改进(%)', '10%准确率提升(%)']
    improvement_colors = ['gold' if x > 0 else 'lightcoral' for x in improvements]

    bars6 = axes[1, 1].bar(improvement_labels, improvements, color=improvement_colors, alpha=0.7)
    axes[1, 1].set_ylabel('改进程度 (%)')
    axes[1, 1].set_title('AKF改进效果')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.5)

    for bar, value in zip(bars6, improvements):
        axes[1, 1].text(bar.get_x() + bar.get_width()/2,
                        bar.get_height() + (0.5 if value > 0 else -1),
                        f'{value:.2f}%', ha='center', va='bottom' if value > 0 else 'top')

    # 6. 综合评分
    # 计算综合评分 (MAE权重40%, RMSE权重30%, 10%准确率权重30%)
    lstm_score = (1/lstm_mae) * 0.4 + (1/lstm_rmse) * 0.3 + lstm_acc_10 * 0.3
    akf_score = (1/akf_mae) * 0.4 + (1/akf_rmse) * 0.3 + akf_acc_10 * 0.3

    scores = [lstm_score, akf_score]
    bars7 = axes[1, 2].bar(methods, scores, color=colors, alpha=0.7)
    axes[1, 2].set_ylabel('综合评分')
    axes[1, 2].set_title('综合性能评分')
    axes[1, 2].grid(True, alpha=0.3)

    for bar, value in zip(bars7, scores):
        axes[1, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.01,
                        f'{value:.2f}', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig('lstm_vs_akf_dashboard.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("对比性能仪表板已保存到: lstm_vs_akf_dashboard.png")

async def main():
    """主函数"""
    print("="*60)
    print("LSTM vs LSTM+AKF 预测效果对比分析")
    print("测试集: v2.txt | 关键指标: 10%阈值准确率")
    print("="*60)
    
    # 1. 训练模型
    model, scaler = train_model_for_visualization()
    if model is None:
        return
    
    # 2. 创建LSTM处理器和LSTM+AKF处理器
    lstm_processor = RealTimeStreamProcessor(
        model=model,
        scaler=scaler,
        window_size=25,
        buffer_size=1000
    )

    # 创建LSTM+AKF处理器
    akf_processor = AdaptiveKalmanFilter(
        model=model,
        scaler=scaler,
        window_size=25
    )

    # 关闭异常值检测以保持数据真实性
    lstm_processor.enable_outlier_detection = False
    print("已关闭异常值检测，保持数据原始性")
    
    # 3. 准备数据 - 使用v2.txt作为测试集
    print("加载测试数据集...")
    test_data_df = pd.read_csv('v2.txt', sep='\t', encoding='utf-8')
    test_data = test_data_df.iloc[:, 1].values  # v2.txt的位移数据

    print(f"使用v2.txt作为测试集: {len(test_data)} 个点")
    print(f"测试数据范围: 时间 {test_data_df.iloc[0,0]:.3f}-{test_data_df.iloc[-1,0]:.3f}ms")
    print(f"位移范围: {test_data.min():.6f}-{test_data.max():.6f}μm")

    # 使用测试数据的前25个点作为历史数据（LSTM窗口大小）
    historical_data = test_data[:25]
    lstm_processor.initialize_with_historical_data(historical_data)
    akf_processor.initialize_buffer(historical_data)

    # 4. 进行对比预测
    num_prediction_points = len(test_data) - 25
    print(f"将对v2.txt进行LSTM vs LSTM+AKF对比预测（共{num_prediction_points}个点）")

    # 收集对比数据
    comparison_data = await collect_comparison_data(lstm_processor, akf_processor, test_data[25:])

    # 5. 生成对比可视化
    create_comparison_visualization(comparison_data)
    create_comparison_dashboard(comparison_data)
    
    # 6. 输出对比统计结果
    print("\n" + "="*60)
    print("📊 LSTM vs LSTM+AKF 对比统计结果")
    print("="*60)
    print("数据分割方案:")
    # 读取数据信息
    v1_data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8').iloc[:, 1].values
    v2_data = pd.read_csv('v2.txt', sep='\t', encoding='utf-8')
    print(f"  训练数据源: v1.txt前1000个点 (共{len(v1_data)}个点)")
    print(f"  测试数据源: v2.txt全部{len(v2_data)}个点")
    print(f"  历史初始化: v2.txt前25个点")
    print(f"  对比预测: v2.txt第26个点开始 (共{len(v2_data)-25}个点)")
    print("-" * 60)

    real_values = np.array(comparison_data['real_values'])
    lstm_predictions = np.array(comparison_data['lstm_predictions'])
    akf_predictions = np.array(comparison_data['akf_predictions'])
    processing_times = np.array(comparison_data['processing_times'])

    lstm_errors = np.abs(real_values - lstm_predictions)
    akf_errors = np.abs(real_values - akf_predictions)

    print(f"处理数据点数: {len(real_values)}")
    print(f"平均处理时间: {np.mean(processing_times)*1000:.2f} ms")
    print("-" * 60)

    # 保存LSTM预测数据到v2_lstm.txt
    print("💾 保存LSTM预测数据到v2_lstm.txt...")
    # 获取对应的时间数据（从v2.txt第26个点开始）
    v2_time_data = v2_data.iloc[25:25+len(lstm_predictions), 0].values
    lstm_df = pd.DataFrame({
        '时间[ms]': v2_time_data,
        '1-5-a1-(位移)-时域[μm]': lstm_predictions
    })
    lstm_df.to_csv('v2_lstm.txt', sep='\t', index=False, encoding='utf-8')
    print(f"✅ LSTM预测数据已保存到v2_lstm.txt ({len(lstm_predictions)}个点)")

    # 保存LSTM+AKF预测数据到v2_akf.txt
    print("💾 保存LSTM+AKF预测数据到v2_akf.txt...")
    akf_df = pd.DataFrame({
        '时间[ms]': v2_time_data,
        '1-5-a1-(位移)-时域[μm]': akf_predictions
    })
    akf_df.to_csv('v2_akf.txt', sep='\t', index=False, encoding='utf-8')
    print(f"✅ LSTM+AKF预测数据已保存到v2_akf.txt ({len(akf_predictions)}个点)")
    print("-" * 60)

    # 计算误差指标
    lstm_mae = np.mean(lstm_errors)
    akf_mae = np.mean(akf_errors)
    lstm_rmse = np.sqrt(np.mean(lstm_errors**2))
    akf_rmse = np.sqrt(np.mean(akf_errors**2))

    print(f"误差指标对比:")
    print(f"  LSTM - MAE: {lstm_mae:.6f} μm, RMSE: {lstm_rmse:.6f} μm")
    print(f"  LSTM+AKF - MAE: {akf_mae:.6f} μm, RMSE: {akf_rmse:.6f} μm")

    mae_improvement = ((lstm_mae - akf_mae) / lstm_mae) * 100
    rmse_improvement = ((lstm_rmse - akf_rmse) / lstm_rmse) * 100
    print(f"  MAE改进: {mae_improvement:.2f}%")
    print(f"  RMSE改进: {rmse_improvement:.2f}%")
    print("-" * 60)

    # 计算准确率
    lstm_relative_errors = np.abs((real_values - lstm_predictions) / real_values) * 100
    akf_relative_errors = np.abs((real_values - akf_predictions) / real_values) * 100

    lstm_acc_1 = np.sum(lstm_relative_errors <= 1.0) / len(lstm_relative_errors) * 100
    lstm_acc_5 = np.sum(lstm_relative_errors <= 5.0) / len(lstm_relative_errors) * 100
    lstm_acc_10 = np.sum(lstm_relative_errors <= 10.0) / len(lstm_relative_errors) * 100

    akf_acc_1 = np.sum(akf_relative_errors <= 1.0) / len(akf_relative_errors) * 100
    akf_acc_5 = np.sum(akf_relative_errors <= 5.0) / len(akf_relative_errors) * 100
    akf_acc_10 = np.sum(akf_relative_errors <= 10.0) / len(akf_relative_errors) * 100

    print(f"准确率对比:")
    print(f"  LSTM - ±1%: {lstm_acc_1:.1f}%, ±5%: {lstm_acc_5:.1f}%, ±10%: {lstm_acc_10:.1f}%")
    print(f"  LSTM+AKF - ±1%: {akf_acc_1:.1f}%, ±5%: {akf_acc_5:.1f}%, ±10%: {akf_acc_10:.1f}%")

    acc_10_improvement = akf_acc_10 - lstm_acc_10
    print(f"  10%准确率提升: {acc_10_improvement:.2f}% ⭐ (关键指标)")
    print("-" * 60)
    print(f"🎯 LSTM 10%阈值准确率: {lstm_acc_10:.2f}%")
    print(f"🎯 LSTM+AKF 10%阈值准确率: {akf_acc_10:.2f}%")

    print("\n" + "="*60)
    print("✅ LSTM vs LSTM+AKF 对比分析完成！")
    print("📁 生成的文件:")
    print("  📊 预测数据文件:")
    print("    - v2_lstm.txt (LSTM预测结果)")
    print("    - v2_akf.txt (LSTM+AKF预测结果)")
    print("  📈 可视化图表:")
    print("    - lstm_vs_akf_comparison.png")
    print("    - lstm_vs_akf_dashboard.png")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
