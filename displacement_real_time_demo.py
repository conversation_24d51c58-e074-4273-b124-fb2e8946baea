"""
基于位移数据的实时LSTM+卡尔曼滤波预测演示
专门针对振动位移信号进行实时预测优化
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
import time
from collections import deque
import warnings
import random

def set_all_seeds(seed=42):
    """设置所有随机种子以确保结果可重复"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

# 设置随机种子
set_all_seeds(42)

# 忽略警告
warnings.filterwarnings("ignore")

# 设置中文字体
try:
    # 基于字体测试结果，使用SimHei字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    USE_CHINESE = True
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    USE_CHINESE = False
    print("使用默认字体")

# 位移专用LSTM模型
class DisplacementLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        # x shape: (batch_size, seq_len, input_size)
        lstm_out, _ = self.lstm(x)
        # 取最后一个时间步的输出
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

# 集成LSTM的卡尔曼滤波器（参考lstm_KF.py.py的实现方式）
class IntegratedLSTMKalmanFilter:
    def __init__(self, model, scaler, window_size=25, initial_process_variance=1e-4, initial_measurement_variance=5e-4):
        """
        集成LSTM预测的卡尔曼滤波器
        LSTM预测直接作用到卡尔曼滤波的预测步骤中
        """
        # LSTM模型相关
        self.model = model
        self.scaler = scaler
        self.window_size = window_size
        self.model.eval()

        # 数据缓冲区用于LSTM预测
        self.data_buffer = deque(maxlen=window_size)

        # 卡尔曼滤波参数（2维状态：位移和加速度）
        self.B = 0  # 外部输入为0
        self.u = 0  # 外部输入为0
        self.K = float('nan')  # 卡尔曼增益
        self.z = float('nan')  # 观测值

        # 状态向量 [位移, 加速度]
        self.x = np.array([0.0, 0.0])  # 状态估计值 [位移, 加速度]
        self.G = np.array([0.0, 0.0])  # 预测状态

        # 状态转移矩阵A (2x2)
        # 假设时间步长dt=1，使用运动学方程：
        # 位移(k+1) = 位移(k) + 0.5 * 加速度(k) * dt^2
        # 加速度(k+1) = 加速度(k) (假设加速度在短时间内保持相对稳定)
        dt = 1.0  # 时间步长
        self.A = np.array([[1.0, 0.5 * dt * dt],
                          [0.0, 1.0]])

        # 过程噪声协方差矩阵Q (2x2)
        # 位移的过程噪声相对较小，加速度的过程噪声可以稍大一些
        self.Q = np.diag([initial_process_variance, initial_process_variance * 2.0])

        # 观测矩阵H (1x2) - 只观测位移
        self.H = np.array([[1.0, 0.0]])

        # 观测噪声协方差矩阵R (1x1)
        self.R = np.array([[initial_measurement_variance]])

        # 估计误差协方差矩阵P (2x2)
        self.P = np.diag([1.0, 1.0])

        # 自适应参数
        self.measurement_history = []
        self.innovation_history = []
        self.max_history = 15
        self.displacement_threshold = 2e-3
        self.adaptation_rate = 0.08

        # 加速度估计相关参数
        self.previous_displacement = 0.0
        self.previous_velocity = 0.0

        # LSTM预测存储（用于对比显示）
        self.lstm_prediction = None

    def initialize_buffer(self, initial_data):
        """初始化LSTM数据缓冲区"""
        if len(initial_data) < self.window_size:
            raise ValueError(f"初始数据长度必须至少为 {self.window_size}")

        # 标准化初始数据
        normalized_data = self.scaler.transform(
            np.array(initial_data[-self.window_size:]).reshape(-1, 1)
        ).flatten()

        # 填充缓冲区
        self.data_buffer.clear()
        for value in normalized_data:
            self.data_buffer.append(value)

        # 初始化状态
        self.x[0] = initial_data[-1]  # 初始位移

        # 估计初始加速度（基于最近几个数据点）
        if len(initial_data) >= 3:
            # 使用最后3个点估计加速度
            recent_displacements = initial_data[-3:]
            # 计算速度：v = (x[i] - x[i-1]) / dt
            v1 = recent_displacements[1] - recent_displacements[0]
            v2 = recent_displacements[2] - recent_displacements[1]
            # 计算加速度：a = (v[i] - v[i-1]) / dt
            initial_acceleration = v2 - v1
            self.x[1] = initial_acceleration
        else:
            self.x[1] = 0.0  # 初始加速度为0

        self.previous_displacement = initial_data[-1]
        self.previous_velocity = 0.0

        print(f"集成LSTM-卡尔曼滤波器已初始化，包含 {len(self.data_buffer)} 个数据点")
        print(f"初始状态: 位移={self.x[0]:.6f}, 加速度={self.x[1]:.6f}")

    def predict_and_update(self, observation=None):
        """
        集成LSTM预测的卡尔曼滤波预测和更新步骤
        参考lstm_KF.py.py中kf_update函数的实现方式
        """
        # 1. 传统卡尔曼预测步骤
        # x_ = A * x + B * u
        x_pred = np.dot(self.A, self.x) + self.B * self.u

        # 2. LSTM预测步骤（关键：LSTM预测与卡尔曼预测融合）
        lstm_displacement_pred = None
        if len(self.data_buffer) >= self.window_size:
            # 准备LSTM输入序列
            input_seq = torch.FloatTensor(list(self.data_buffer)).unsqueeze(0).unsqueeze(-1)

            # LSTM预测
            with torch.no_grad():
                normalized_prediction = self.model(input_seq).item()

            # 反标准化得到位移预测
            lstm_displacement_pred = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]

            # 动态融合策略：根据信号变化程度调整权重
            # 计算最近的变化程度
            if len(self.measurement_history) >= 2:
                recent_change = abs(self.measurement_history[-1] - self.measurement_history[-2])
                # 当变化较大时，增加LSTM权重（LSTM对趋势更敏感）
                if recent_change > self.displacement_threshold * 2:
                    lstm_weight = 0.8  # 快速变化时增加LSTM权重
                elif recent_change > self.displacement_threshold:
                    lstm_weight = 0.75  # 中等变化时适中权重
                else:
                    lstm_weight = 0.7  # 平缓变化时标准权重
            else:
                lstm_weight = 0.7  # 默认权重

            kalman_weight = 1.0 - lstm_weight  # 卡尔曼权重

            # 融合预测：结合LSTM预测和卡尔曼物理模型预测
            x_pred[0] = lstm_weight * lstm_displacement_pred + kalman_weight * x_pred[0]

            # 更新数据缓冲区（如果有新观测值）
            if observation is not None:
                normalized_obs = self.scaler.transform([[observation]])[0, 0]
                self.data_buffer.append(normalized_obs)

        # 保存LSTM预测用于对比
        self.lstm_prediction = lstm_displacement_pred

        # 3. 预测误差协方差更新
        # P_ = A * P * A^T + Q
        P_pred = np.dot(np.dot(self.A, self.P), self.A.T) + self.Q

        # 4. 如果有观测值，进行更新步骤
        if observation is not None:
            self.z = np.array([observation])

            # 记录测量历史用于自适应调整
            self.measurement_history.append(observation)
            if len(self.measurement_history) > self.max_history:
                self.measurement_history.pop(0)

            # 计算卡尔曼增益
            # K = P_ * H^T * (H * P_ * H^T + R)^(-1)
            S = np.dot(np.dot(self.H, P_pred), self.H.T) + self.R
            self.K = np.dot(np.dot(P_pred, self.H.T), np.linalg.inv(S))

            # 计算创新
            innovation = self.z - np.dot(self.H, x_pred)
            self.innovation_history.append(innovation[0])
            if len(self.innovation_history) > self.max_history:
                self.innovation_history.pop(0)

            # 自适应调整
            self._adaptive_adjustment()

            # 状态更新
            self.x = x_pred + np.dot(self.K, innovation)

            # 协方差更新
            self.P = P_pred - np.dot(np.dot(self.K, self.H), P_pred)

            # 更新加速度估计（基于观测到的位移变化）
            self._update_acceleration_estimate(observation)
        else:
            # 没有观测值时，只进行预测
            self.x = x_pred
            self.P = P_pred

        # 保存预测状态
        self.G = x_pred

        return self.x[0]  # 返回位移估计值

    def _update_acceleration_estimate(self, current_displacement):
        """更新加速度估计（基于观测到的位移变化）"""
        if len(self.measurement_history) >= 2:
            # 计算当前速度
            current_velocity = current_displacement - self.previous_displacement

            # 计算加速度变化
            acceleration_change = current_velocity - self.previous_velocity

            # 使用指数平滑更新加速度估计
            alpha = 0.3  # 平滑系数
            self.x[1] = (1 - alpha) * self.x[1] + alpha * acceleration_change

            # 更新历史值
            self.previous_velocity = current_velocity

        self.previous_displacement = current_displacement

    def _adaptive_adjustment(self):
        """基于位移信号特性的自适应调整（考虑位移和加速度）"""
        if len(self.measurement_history) < 3:
            return

        recent_measurements = np.array(self.measurement_history[-3:])

        # 计算位移变化的平缓程度
        displacement_changes = np.abs(np.diff(recent_measurements))
        avg_change = np.mean(displacement_changes)
        max_change = np.max(displacement_changes)

        # 计算加速度变化程度（二阶差分）
        if len(recent_measurements) >= 3:
            acceleration_changes = np.abs(np.diff(displacement_changes))
            avg_acceleration_change = np.mean(acceleration_changes)
        else:
            avg_acceleration_change = 0

        # 检测快速变化模式（特别针对末尾快速上升）
        if max_change > self.displacement_threshold * 3:
            # 检测到非常大的变化，大幅增加过程噪声以提高适应性
            self.Q[0, 0] = min(1e-2, self.Q[0, 0] * 1.5)  # 位移过程噪声
            self.Q[1, 1] = min(2e-2, self.Q[1, 1] * 1.8)  # 加速度过程噪声
        elif max_change > self.displacement_threshold * 2 or avg_acceleration_change > self.displacement_threshold:
            # 检测到较大变化或加速度变化，适度增加过程噪声
            self.Q[0, 0] = min(5e-3, self.Q[0, 0] * 1.2)  # 位移过程噪声
            self.Q[1, 1] = min(1e-2, self.Q[1, 1] * 1.3)  # 加速度过程噪声
        elif avg_change > self.displacement_threshold:
            # 变化适中，轻微增加过程噪声
            self.Q[0, 0] = min(2e-3, self.Q[0, 0] * 1.05)
            self.Q[1, 1] = min(5e-3, self.Q[1, 1] * 1.1)
        else:
            # 变化较小，适度减少过程噪声
            self.Q[0, 0] = max(1e-6, self.Q[0, 0] * 0.99)
            self.Q[1, 1] = max(1e-5, self.Q[1, 1] * 0.98)

        # 基于创新序列调整测量噪声
        if len(self.innovation_history) >= 3:
            innovation_var = np.var(self.innovation_history[-3:])
            if innovation_var > 0:
                target_R = innovation_var * 0.6
                self.R[0, 0] = (1 - self.adaptation_rate) * self.R[0, 0] + self.adaptation_rate * target_R
                self.R[0, 0] = np.clip(self.R[0, 0], 1e-6, 1e-2)

# 集成LSTM-卡尔曼滤波实时预测器
class IntegratedLSTMKalmanPredictor:
    def __init__(self, model, scaler, window_size=25, use_integrated_kalman=True):
        """
        集成LSTM-卡尔曼滤波实时预测器
        使用参考lstm_KF.py.py的集成方式，LSTM预测直接作用到卡尔曼滤波的预测过程中
        """
        self.model = model
        self.scaler = scaler
        self.window_size = window_size
        self.use_integrated_kalman = use_integrated_kalman
        self.model.eval()

        # 集成LSTM-卡尔曼滤波器
        if self.use_integrated_kalman:
            self.integrated_filter = IntegratedLSTMKalmanFilter(
                model=model,
                scaler=scaler,
                window_size=window_size,
                initial_process_variance=1e-4,
                initial_measurement_variance=5e-4
            )
        else:
            # 如果不使用集成滤波器，则只使用LSTM
            self.data_buffer = deque(maxlen=window_size)

        # 性能统计
        self.prediction_times = []
        self.predictions_made = 0

    def initialize_buffer(self, initial_data):
        """初始化数据缓冲区"""
        if len(initial_data) < self.window_size:
            raise ValueError(f"初始数据长度必须至少为 {self.window_size}")

        if self.use_integrated_kalman:
            # 使用集成滤波器的初始化
            self.integrated_filter.initialize_buffer(initial_data)
        else:
            # 标准化初始数据
            normalized_data = self.scaler.transform(
                np.array(initial_data[-self.window_size:]).reshape(-1, 1)
            ).flatten()

            # 填充缓冲区
            self.data_buffer.clear()
            for value in normalized_data:
                self.data_buffer.append(value)

        print(f"集成LSTM-卡尔曼预测器已初始化，包含 {len(initial_data)} 个历史数据点")

    def predict_next(self, new_observation=None):
        """
        预测下一个位移值
        new_observation: 新的观测值（用于卡尔曼滤波更新）
        """
        start_time = time.time()

        if self.use_integrated_kalman:
            # 使用集成LSTM-卡尔曼滤波器进行预测
            # 这里LSTM预测与卡尔曼预测融合
            integrated_prediction = self.integrated_filter.predict_and_update(new_observation)

            # 获取纯LSTM预测（用于对比显示）
            if self.integrated_filter.lstm_prediction is not None:
                lstm_only_prediction = self.integrated_filter.lstm_prediction
            else:
                # 如果没有LSTM预测，计算一个
                if len(self.integrated_filter.data_buffer) >= self.window_size:
                    input_seq = torch.FloatTensor(list(self.integrated_filter.data_buffer)).unsqueeze(0).unsqueeze(-1)
                    with torch.no_grad():
                        normalized_prediction = self.model(input_seq).item()
                    lstm_only_prediction = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]
                else:
                    lstm_only_prediction = integrated_prediction

            kalman_prediction = integrated_prediction

        else:
            # 如果不使用集成滤波器，只进行LSTM预测
            if new_observation is not None:
                normalized_point = self.scaler.transform([[new_observation]])[0, 0]
                self.data_buffer.append(normalized_point)

            if len(self.data_buffer) < self.window_size:
                raise ValueError(f"缓冲区数据不足，需要 {self.window_size} 个点")

            input_seq = torch.FloatTensor(list(self.data_buffer)).unsqueeze(0).unsqueeze(-1)
            with torch.no_grad():
                normalized_prediction = self.model(input_seq).item()

            lstm_only_prediction = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]
            kalman_prediction = lstm_only_prediction

        # 记录性能
        prediction_time = time.time() - start_time
        self.prediction_times.append(prediction_time)
        self.predictions_made += 1

        return lstm_only_prediction, kalman_prediction, prediction_time
    
    def get_performance_stats(self):
        """获取性能统计"""
        if not self.prediction_times:
            return None
        
        return {
            'total_predictions': self.predictions_made,
            'avg_prediction_time': np.mean(self.prediction_times),
            'max_prediction_time': np.max(self.prediction_times),
            'min_prediction_time': np.min(self.prediction_times),
            'predictions_per_second': 1.0 / np.mean(self.prediction_times)
        }

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

def displacement_real_time_demo():
    """集成LSTM-卡尔曼滤波位移数据实时预测演示（位移+加速度状态）"""
    print("=== 集成LSTM-卡尔曼滤波位移数据实时预测演示 ===\n")
    print("参考lstm_KF.py.py的实现方式，LSTM预测直接集成到卡尔曼滤波的预测步骤中")
    print("卡尔曼滤波器状态向量: [位移, 加速度]")
    print("使用1_2.py生成的1000个点平缓振动数据进行实时预测演示")
    print("数据特征: 多频率成分、渐进性波动、幅度调制、适度噪声")

    # 1. 加载位移数据
    try:
        df = pd.read_csv('smooth_vibration_data.csv')
        displacement_data = df['displacement'].values.astype(float)

        print(f"\n平缓振动位移数据加载成功:")
        print(f"  数据点数: {len(displacement_data)}")
        print(f"  位移范围: {np.min(displacement_data):.4f} ~ {np.max(displacement_data):.4f}")
        print(f"  位移标准差: {np.std(displacement_data):.4f}")
        print(f"  峰峰值: {np.max(displacement_data) - np.min(displacement_data):.4f}")
        print(f"  数据来源: 1_2.py生成的1000个点平缓振动数据")

    except FileNotFoundError:
        print("错误: 未找到 smooth_vibration_data.csv 文件")
        print("请先运行 1_2.py 生成平缓振动数据")
        return
    
    # 2. 数据预处理
    scaler = MinMaxScaler(feature_range=(-1, 1))
    displacement_normalized = scaler.fit_transform(displacement_data.reshape(-1, 1))
    displacement_normalized = torch.FloatTensor(displacement_normalized.flatten())
    
    # 3. 快速训练平缓振动预测模型
    window_size = 25  # 增大窗口以更好地捕捉平缓振动的规律性
    X, y = create_sequences(displacement_normalized.numpy(), window_size)

    # 转换为PyTorch张量
    X = torch.FloatTensor(X).unsqueeze(-1)  # (batch, seq, feature)
    y = torch.FloatTensor(y)

    # 初始化模型（适中的隐藏层大小处理平缓信号）
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)  # 适中的学习率

    print(f"\n开始训练平缓振动预测模型...")
    print(f"模型参数: 窗口大小={window_size}, 隐藏层大小=96")

    # 适中的训练轮次处理平缓信号
    epochs = 120
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()

        if epoch % 30 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')

    print("平缓振动模型训练完成！")
    
    # 4. 实时预测演示
    # 使用前80%的数据作为"历史数据"，后20%模拟"实时数据流"
    split_point = int(len(displacement_data) * 0.8)
    historical_data = displacement_data[:split_point]
    streaming_data = displacement_data[split_point:]
    
    print(f"\n=== 实时预测设置 ===")
    print(f"历史数据长度: {len(historical_data)} 点")
    print(f"实时流数据长度: {len(streaming_data)} 点")
    print(f"数据分割点: 80%")
    
    # 创建集成LSTM-卡尔曼滤波实时预测器
    predictor = IntegratedLSTMKalmanPredictor(model, scaler, window_size, use_integrated_kalman=True)
    predictor.initialize_buffer(historical_data)

    # 开始实时预测
    print(f"\n开始集成LSTM-卡尔曼滤波实时预测...")
    print(f"预测方式: LSTM预测直接集成到卡尔曼滤波的预测步骤中（参考lstm_KF.py.py）")
    print(f"数据特点: 多频率成分、渐进性波动、幅度调制、适度噪声")

    real_values = []
    lstm_predictions = []
    kalman_predictions = []
    prediction_times = []

    for i, true_value in enumerate(streaming_data):
        # 预测下一个位移值（不提供观测值，纯预测）
        lstm_pred, kalman_pred, pred_time = predictor.predict_next()

        # 记录结果
        real_values.append(true_value)
        lstm_predictions.append(lstm_pred)
        kalman_predictions.append(kalman_pred)
        prediction_times.append(pred_time)

        # 将真实观测值提供给预测器进行卡尔曼滤波更新
        if i < len(streaming_data) - 1:
            # 下一次预测时会使用这个观测值进行卡尔曼滤波更新
            predictor.predict_next(true_value)

        # 显示进度
        if (i + 1) % 50 == 0:  # 增加显示间隔
            print(f"已处理 {i + 1}/{len(streaming_data)} 个位移点，"
                  f"平均预测时间: {np.mean(prediction_times[-50:]):.4f}秒")
    
    return real_values, lstm_predictions, kalman_predictions, predictor

def visualize_displacement_results(real_values, lstm_predictions, kalman_predictions):
    """可视化集成LSTM-卡尔曼滤波预测结果"""
    print(f"\n生成集成LSTM-卡尔曼滤波预测结果可视化...")

    plt.figure(figsize=(16, 12))

    time_steps = np.arange(len(real_values))

    # 子图1: 位移预测结果对比
    plt.subplot(3, 1, 1)
    plt.plot(time_steps, real_values, 'b-', label='真实位移' if USE_CHINESE else 'Real Displacement',
             alpha=0.8, linewidth=2)
    plt.plot(time_steps, lstm_predictions, 'r--', label='LSTM预测' if USE_CHINESE else 'LSTM Prediction',
             alpha=0.7, linewidth=1.5)
    plt.plot(time_steps, kalman_predictions, 'g-', label='集成LSTM-卡尔曼(位移+加速度)' if USE_CHINESE else 'Integrated LSTM-Kalman(Disp+Accel)',
             alpha=0.9, linewidth=2)

    plt.title('集成LSTM-卡尔曼滤波位移实时预测结果对比(位移+加速度状态)' if USE_CHINESE else 'Integrated LSTM-Kalman Filter Real-time Prediction (Displacement+Acceleration State)',
              fontsize=14, fontweight='bold')
    plt.xlabel('时间步' if USE_CHINESE else 'Time Steps')
    plt.ylabel('位移 (m)' if USE_CHINESE else 'Displacement (m)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    lstm_mae = np.mean(np.abs(np.array(real_values) - np.array(lstm_predictions)))
    kalman_mae = np.mean(np.abs(np.array(real_values) - np.array(kalman_predictions)))

    info_text = f'LSTM MAE: {lstm_mae:.6f}m\nKalman MAE: {kalman_mae:.6f}m'
    plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # 子图2: 预测误差分析
    plt.subplot(3, 1, 2)
    lstm_errors = np.abs(np.array(real_values) - np.array(lstm_predictions))
    kalman_errors = np.abs(np.array(real_values) - np.array(kalman_predictions))

    plt.plot(time_steps, lstm_errors, 'r-', alpha=0.7, label='LSTM误差' if USE_CHINESE else 'LSTM Error',
             linewidth=1.5)
    plt.plot(time_steps, kalman_errors, 'g-', alpha=0.9, label='集成滤波误差' if USE_CHINESE else 'Integrated Filter Error',
             linewidth=2)

    plt.title('位移预测误差对比' if USE_CHINESE else 'Displacement Prediction Error Comparison',
              fontsize=12)
    plt.xlabel('时间步' if USE_CHINESE else 'Time Steps')
    plt.ylabel('绝对误差 (m)' if USE_CHINESE else 'Absolute Error (m)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 子图3: 误差改善分析
    plt.subplot(3, 1, 3)
    error_improvement = lstm_errors - kalman_errors
    plt.plot(time_steps, error_improvement, 'purple', alpha=0.8, linewidth=2)
    plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    plt.title('集成LSTM-卡尔曼滤波误差改善效果' if USE_CHINESE else 'Integrated LSTM-Kalman Filter Error Improvement',
              fontsize=12)
    plt.xlabel('时间步' if USE_CHINESE else 'Time Steps')
    plt.ylabel('误差减少量 (m)' if USE_CHINESE else 'Error Reduction (m)')
    plt.grid(True, alpha=0.3)

    # 添加改善统计
    avg_improvement = np.mean(error_improvement)
    improvement_text = f'平均改善: {avg_improvement:.6f}m' if USE_CHINESE else f'Avg Improvement: {avg_improvement:.6f}m'
    plt.text(0.02, 0.98, improvement_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

    plt.tight_layout()
    plt.savefig('integrated_lstm_kalman_disp_accel_prediction.png', dpi=300, bbox_inches='tight')
    plt.show()

    print(f"预测结果图像已保存到: integrated_lstm_kalman_disp_accel_prediction.png")

    # 保存详细结果
    results_df = pd.DataFrame({
        'time_step': time_steps,
        'real_displacement': real_values,
        'lstm_prediction': lstm_predictions,
        'integrated_kalman_disp_accel_prediction': kalman_predictions,
        'lstm_error': lstm_errors,
        'integrated_kalman_disp_accel_error': kalman_errors,
        'error_improvement': error_improvement
    })

    results_df.to_csv('integrated_lstm_kalman_disp_accel_results.csv', index=False)
    print(f"详细结果已保存到: integrated_lstm_kalman_disp_accel_results.csv")

if __name__ == "__main__":
    results = displacement_real_time_demo()
    
    if results is not None:
        real_values, lstm_predictions, kalman_predictions, predictor = results
        
        # 性能分析
        stats = predictor.get_performance_stats()
        
        print(f"\n=== 集成LSTM-卡尔曼滤波预测性能统计 ===")
        print(f"总预测次数: {stats['total_predictions']}")
        print(f"平均预测时间: {stats['avg_prediction_time']:.4f} 秒")
        print(f"预测速率: {stats['predictions_per_second']:.1f} 次/秒")

        # 准确性分析
        lstm_mae = np.mean(np.abs(np.array(real_values) - np.array(lstm_predictions)))
        lstm_rmse = np.sqrt(np.mean((np.array(real_values) - np.array(lstm_predictions)) ** 2))

        kalman_mae = np.mean(np.abs(np.array(real_values) - np.array(kalman_predictions)))
        kalman_rmse = np.sqrt(np.mean((np.array(real_values) - np.array(kalman_predictions)) ** 2))

        print(f"\n=== 集成LSTM-卡尔曼滤波预测准确性对比（位移+加速度状态） ===")
        print(f"LSTM原始预测:")
        print(f"  平均绝对误差 (MAE): {lstm_mae:.6f} m")
        print(f"  均方根误差 (RMSE): {lstm_rmse:.6f} m")
        print(f"集成LSTM-卡尔曼滤波（位移+加速度状态）:")
        print(f"  平均绝对误差 (MAE): {kalman_mae:.6f} m")
        print(f"  均方根误差 (RMSE): {kalman_rmse:.6f} m")

        mae_improvement = ((lstm_mae - kalman_mae) / lstm_mae) * 100
        rmse_improvement = ((lstm_rmse - kalman_rmse) / lstm_rmse) * 100
        print(f"集成滤波改善:")
        print(f"  MAE改善: {mae_improvement:.2f}%")
        print(f"  RMSE改善: {rmse_improvement:.2f}%")

        # 可视化结果
        visualize_displacement_results(real_values, lstm_predictions, kalman_predictions)
