"""
实时预测视频生成器
生成LSTM+AKF实时振动预测的动态视频演示
"""

import asyncio
import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from real_time_stream_processor import RealTimeStreamProcessor, SimulatedDataSource
import warnings
from collections import deque

warnings.filterwarnings("ignore")

# 设置中文字体和高质量绘图
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['figure.dpi'] = 100
    plt.rcParams['savefig.dpi'] = 200
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    print("使用默认字体")

# LSTM模型定义
class DisplacementLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

def train_model_for_video():
    """训练模型用于视频演示"""
    print("正在训练LSTM模型...")
    
    # 1. 加载训练数据
    try:
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values
        print(f"加载数据成功，总数据点数: {len(displacement_data)}")
        
        # 使用前1000个点作为训练数据
        train_data = displacement_data[:1000]
        print(f"使用前1000个点进行模型训练")
        
    except FileNotFoundError:
        print("错误: 未找到v1.txt文件")
        return None, None
    
    # 2. 数据预处理
    scaler = MinMaxScaler(feature_range=(-1, 1))
    train_normalized = scaler.fit_transform(train_data.reshape(-1, 1))
    train_normalized = torch.FloatTensor(train_normalized.flatten())
    
    # 3. 创建训练序列
    window_size = 25
    X, y = create_sequences(train_normalized.numpy(), window_size)
    
    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)
    
    print(f"训练序列数量: {len(X)}, 窗口大小: {window_size}")
    
    # 4. 训练模型
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)
    
    epochs = 100
    print(f"开始训练，共{epochs}个epoch...")
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()
        
        if epoch % 25 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')
    
    model.eval()
    print(f"模型训练完成！最终损失: {loss.item():.6f}")
    
    return model, scaler

class RealTimeVideoGenerator:
    """实时预测视频生成器"""
    
    def __init__(self, processor, data_source, max_points=200):
        self.processor = processor
        self.data_source = data_source
        self.max_points = max_points
        
        # 数据存储
        self.real_values = deque(maxlen=max_points)
        self.predictions = deque(maxlen=max_points)
        self.errors = deque(maxlen=max_points)
        self.processing_times = deque(maxlen=max_points)
        self.time_steps = deque(maxlen=max_points)
        
        # 统计信息
        self.current_step = 0
        self.total_points = 0
        self.avg_error = 0
        self.avg_processing_time = 0
        
        # 创建图形
        self.setup_figure()
        
    def setup_figure(self):
        """设置图形布局"""
        self.fig = plt.figure(figsize=(16, 12))
        self.fig.suptitle('LSTM+AKF实时振动预测视频演示', fontsize=16, fontweight='bold')
        
        # 创建子图
        gs = self.fig.add_gridspec(3, 2, height_ratios=[2, 1, 1], hspace=0.3, wspace=0.3)
        
        # 主预测图
        self.ax_main = self.fig.add_subplot(gs[0, :])
        self.line_real, = self.ax_main.plot([], [], 'b-', linewidth=2, label='真实位移', alpha=0.8)
        self.line_pred, = self.ax_main.plot([], [], 'r--', linewidth=2, label='LSTM+AKF预测', alpha=0.8)
        self.ax_main.set_xlabel('时间步')
        self.ax_main.set_ylabel('位移 (μm)')
        self.ax_main.set_title('实时预测对比')
        self.ax_main.legend()
        self.ax_main.grid(True, alpha=0.3)
        
        # 误差图
        self.ax_error = self.fig.add_subplot(gs[1, 0])
        self.line_error, = self.ax_error.plot([], [], 'g-', linewidth=2, alpha=0.8)
        self.ax_error.set_xlabel('时间步')
        self.ax_error.set_ylabel('绝对误差 (μm)')
        self.ax_error.set_title('预测误差')
        self.ax_error.grid(True, alpha=0.3)
        
        # 处理时间图
        self.ax_time = self.fig.add_subplot(gs[1, 1])
        self.line_time, = self.ax_time.plot([], [], 'purple', linewidth=2, alpha=0.8)
        self.ax_time.set_xlabel('时间步')
        self.ax_time.set_ylabel('处理时间 (ms)')
        self.ax_time.set_title('处理性能')
        self.ax_time.grid(True, alpha=0.3)
        
        # 统计信息显示
        self.ax_stats = self.fig.add_subplot(gs[2, :])
        self.ax_stats.axis('off')
        self.stats_text = self.ax_stats.text(0.1, 0.8, '', fontsize=12, fontweight='bold',
                                           transform=self.ax_stats.transAxes,
                                           bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
    async def update_data(self):
        """更新一个数据点"""
        # 从数据源读取数据
        raw_data = await self.data_source.read_data()
        
        if raw_data is not None:
            # 记录处理开始时间
            start_time = time.time()
            
            # 进行预测
            prediction = self.processor.process_single_point(raw_data)
            
            # 计算处理时间
            processing_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            if prediction is not None:
                # 存储数据
                self.real_values.append(raw_data)
                self.predictions.append(prediction)
                self.errors.append(abs(raw_data - prediction))
                self.processing_times.append(processing_time)
                self.time_steps.append(self.current_step)
                
                self.current_step += 1
                self.total_points += 1
                
                # 更新统计信息
                if len(self.errors) > 0:
                    self.avg_error = np.mean(list(self.errors))
                    self.avg_processing_time = np.mean(list(self.processing_times))
                
                return True
        
        return False
    
    def update_plot(self, frame):
        """更新图形显示"""
        # 异步更新数据
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(self.update_data())
        loop.close()
        
        if success and len(self.real_values) > 1:
            # 更新数据
            time_data = list(self.time_steps)
            real_data = list(self.real_values)
            pred_data = list(self.predictions)
            error_data = list(self.errors)
            time_data_ms = list(self.processing_times)
            
            # 更新主预测图
            self.line_real.set_data(time_data, real_data)
            self.line_pred.set_data(time_data, pred_data)
            
            # 更新误差图
            self.line_error.set_data(time_data, error_data)
            
            # 更新处理时间图
            self.line_time.set_data(time_data, time_data_ms)
            
            # 自动调整坐标轴
            for ax, data in [(self.ax_main, real_data + pred_data),
                           (self.ax_error, error_data),
                           (self.ax_time, time_data_ms)]:
                if len(data) > 0:
                    ax.set_xlim(max(0, self.current_step - self.max_points), self.current_step + 10)
                    data_min, data_max = min(data), max(data)
                    margin = (data_max - data_min) * 0.1 if data_max != data_min else 0.1
                    ax.set_ylim(data_min - margin, data_max + margin)
            
            # 更新统计信息
            if len(self.errors) > 0:
                max_error = max(error_data)
                min_error = min(error_data)
                current_error = error_data[-1] if error_data else 0
                current_time = time_data_ms[-1] if time_data_ms else 0
                
                stats_info = f"""实时统计信息:
当前数据点: {self.current_step}    当前误差: {current_error:.6f} μm    当前处理时间: {current_time:.2f} ms
平均误差: {self.avg_error:.6f} μm    最大误差: {max_error:.6f} μm    最小误差: {min_error:.6f} μm
平均处理时间: {self.avg_processing_time:.2f} ms    处理速率: {1000/self.avg_processing_time:.1f} 次/秒"""
                
                self.stats_text.set_text(stats_info)
        
        return (self.line_real, self.line_pred, self.line_error, 
                self.line_time, self.stats_text)

async def generate_real_time_video():
    """生成实时预测视频"""
    print("="*60)
    print("LSTM+AKF实时预测视频生成器")
    print("="*60)
    
    # 1. 训练模型
    model, scaler = train_model_for_video()
    if model is None:
        return
    
    # 2. 创建实时处理器
    processor = RealTimeStreamProcessor(
        model=model,
        scaler=scaler,
        window_size=25,
        buffer_size=1000
    )
    
    # 关闭异常值检测
    processor.enable_outlier_detection = False
    print("已关闭异常值检测，保持数据原始性")
    
    # 3. 准备数据
    data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
    displacement_data = data.iloc[:, 1].values
    test_data = displacement_data[1000:]  # 后1000个点
    
    # 初始化处理器
    historical_data = test_data[:25]
    processor.initialize_with_historical_data(historical_data)
    
    # 4. 创建数据源
    data_source = SimulatedDataSource('v1.txt', sampling_rate=50)  # 50Hz用于视频演示
    data_source.current_index = 1025
    data_source.displacement_data = displacement_data
    
    print("数据准备完成:")
    print("  训练数据: 第1-1000个数据点")
    print("  测试数据: 第1001-2000个数据点")
    print("  视频演示: 第1026-1225个数据点 (200个点)")
    print("  采样频率: 50Hz (适合视频观看)")
    
    # 5. 创建视频生成器
    video_generator = RealTimeVideoGenerator(processor, data_source, max_points=200)
    
    # 6. 生成动画
    print("\n开始生成实时预测视频...")
    print("视频参数:")
    print("  帧率: 10 FPS")
    print("  总帧数: 200帧")
    print("  视频时长: 20秒")
    print("  分辨率: 1600x1200")
    
    # 创建动画
    anim = animation.FuncAnimation(
        video_generator.fig,
        video_generator.update_plot,
        frames=200,  # 200帧
        interval=100,  # 100ms间隔 = 10 FPS
        blit=True,
        repeat=False
    )
    
    # 保存为MP4视频
    try:
        print("\n正在保存视频文件...")
        anim.save('lstm_akf_real_time_prediction.mp4', 
                 writer='ffmpeg',
                 fps=10,
                 bitrate=2000,
                 extra_args=['-vcodec', 'libx264'])
        print("✅ 视频保存成功: lstm_akf_real_time_prediction.mp4")
        
    except Exception as e:
        print(f"❌ 视频保存失败: {e}")
        print("尝试保存为GIF格式...")
        try:
            anim.save('lstm_akf_real_time_prediction.gif', 
                     writer='pillow',
                     fps=5)
            print("✅ GIF保存成功: lstm_akf_real_time_prediction.gif")
        except Exception as e2:
            print(f"❌ GIF保存也失败: {e2}")
            print("显示实时动画窗口...")
            plt.show()
    
    print("\n" + "="*60)
    print("实时预测视频生成完成！")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(generate_real_time_video())
