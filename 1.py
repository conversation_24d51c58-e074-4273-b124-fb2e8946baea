# Step 1: 生成振动信号（位移）并保存为 CSV
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import integrate
import warnings

# 忽略字体警告
warnings.filterwarnings("ignore")

# 设置中文字体支持
try:
    # 基于字体测试结果，使用SimHei字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False  # 禁用Unicode减号
    plt.rcParams['font.family'] = 'sans-serif'  # 确保字体族设置
    USE_CHINESE = True
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False  # 即使中文字体失败也禁用Unicode减号
    USE_CHINESE = False
    print("使用默认字体，已禁用Unicode减号")

# 设置随机种子以确保数据生成可重复
np.random.seed(42)

# 时间序列
t = np.linspace(0, 10, 1000)  # 1000 个时间点，10 秒钟
dt = t[1] - t[0]  # 时间步长

# 方法1: 生成适合卡尔曼滤波的振动信号（简化频率成分，高斯白噪声）
# 模拟简化的振动信号：少量频率成分 + 高斯白噪声

# 简化的频率成分（减少到2-3个主要频率）
base_signal = (0.04 * np.sin(2 * np.pi * 2 * t) +                # 主频率2Hz
               0.02 * np.sin(2 * np.pi * 5 * t) +                # 次频率5Hz
               0.01 * np.sin(2 * np.pi * 0.5 * t))               # 低频成分0.5Hz

# 添加高斯白噪声（满足卡尔曼滤波要求）
# 噪声标准差设置为信号幅度的10-15%，确保信噪比合理
noise_std = 0.005  # 噪声标准差
noise = noise_std * np.random.randn(len(t))

# 合成最终的振动信号（仅包含基础频率成分和高斯白噪声）
displacement_signal = base_signal + noise

# 方法2: 从加速度积分得到位移（备选方案）
# acceleration = 0.5 * np.sin(2 * np.pi * 2 * t) + 0.3 * np.sin(2 * np.pi * 5 * t) + 0.1 * np.random.randn(len(t))
# velocity = integrate.cumtrapz(acceleration, t, initial=0)
# displacement_from_accel = integrate.cumtrapz(velocity, t, initial=0)

# 使用直接生成的位移信号
signal = displacement_signal

print(f"适合卡尔曼滤波的振动信号统计信息 (1000个数据点):")
print(f"  时间范围: 0 ~ {t[-1]:.1f} 秒")
print(f"  数据点数: {len(signal)}")
print(f"  采样频率: {1/dt:.1f} Hz")
print(f"  最大值: {np.max(signal):.4f} m")
print(f"  最小值: {np.min(signal):.4f} m")
print(f"  标准差: {np.std(signal):.4f} m")
print(f"  峰峰值: {np.max(signal) - np.min(signal):.4f} m")
print(f"  噪声标准差: {noise_std:.4f} m")
print(f"  信号特征: 简化频率成分 + 高斯白噪声，适合卡尔曼滤波")

# 保存为 CSV 文件
df = pd.DataFrame({'time': t, 'displacement': signal})
df.to_csv('simulated_vibration.csv', index=False)

# 可视化
plt.figure(figsize=(12, 8))

plt.subplot(2, 1, 1)
plt.plot(t, signal, 'b-', linewidth=1.5)
if USE_CHINESE:
    plt.title('适合卡尔曼滤波的振动位移信号 (10秒, 1000点, 简化频率成分)')
    plt.xlabel('时间 (s)')
    plt.ylabel('位移 (m)')
else:
    plt.title('Kalman Filter Ready Vibration Signal (10s, 1000pts, simplified frequency components)')
    plt.xlabel('Time (s)')
    plt.ylabel('Displacement (m)')
plt.grid(True, alpha=0.3)

# 添加频谱分析
plt.subplot(2, 1, 2)
from scipy.fft import fft, fftfreq
N = len(signal)
yf = fft(signal)
xf = fftfreq(N, dt)[:N//2]
plt.plot(xf, 2.0/N * np.abs(yf[0:N//2]), 'r-', linewidth=1.5)
if USE_CHINESE:
    plt.title('位移信号频谱')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值')
else:
    plt.title('Displacement Signal Spectrum')
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Amplitude')
plt.grid(True, alpha=0.3)
plt.xlim(0, 10)

plt.tight_layout()
plt.savefig('kalman_ready_vibration.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"\n适合卡尔曼滤波的振动数据已保存到: simulated_vibration.csv")
print(f"振动信号图像已保存到: kalman_ready_vibration.png")
print(f"\n信号特征说明:")
print(f"- 数据点数: 1000个点，时间范围10秒")
print(f"- 简化的频率成分: 主频2Hz, 次频5Hz, 低频0.5Hz")
print(f"- 高斯白噪声: 标准差{noise_std:.4f}m")
print(f"- 移除了冲击、突变、非线性成分")
print(f"- 移除了调制信号和间歇性振动")
print(f"- 信号平滑，满足卡尔曼滤波的线性系统假设")
print(f"- 噪声满足高斯分布，符合卡尔曼滤波要求")
