"""
计算±10%准确率
基于v1.txt前1000点训练，后1000点测试的结果
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler

# LSTM模型定义
class DisplacementLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

def quick_accuracy_calculation():
    """快速计算±10%准确率"""
    print("="*60)
    print("计算v1.txt训练 + v2.txt测试的±10%准确率")
    print("="*60)

    # 1. 加载训练数据
    train_data_df = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
    train_data = train_data_df.iloc[:1000, 1].values  # 前1000个点

    # 2. 加载测试数据
    test_data_df = pd.read_csv('v2.txt', sep='\t', encoding='utf-8')
    test_data = test_data_df.iloc[:, 1].values  # v2.txt全部数据

    print(f"训练数据: v1.txt前1000个点")
    print(f"测试数据: v2.txt全部{len(test_data)}个点")
    print(f"测试数据范围: 时间 {test_data_df.iloc[0,0]:.3f}-{test_data_df.iloc[-1,0]:.3f}ms")
    print(f"测试位移范围: {test_data.min():.6f}-{test_data.max():.6f}μm")
    
    # 3. 数据预处理
    scaler = MinMaxScaler(feature_range=(-1, 1))
    train_normalized = scaler.fit_transform(train_data.reshape(-1, 1))
    train_normalized = torch.FloatTensor(train_normalized.flatten())
    
    # 4. 创建训练序列
    window_size = 25
    X, y = create_sequences(train_normalized.numpy(), window_size)
    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)
    
    print(f"训练序列数量: {len(X)}")
    
    # 5. 快速训练模型
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)
    
    print("开始快速训练...")
    for epoch in range(50):  # 减少训练轮次以加快速度
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()
        
        if epoch % 10 == 0:
            print(f'Epoch {epoch:2d}: Loss = {loss.item():.6f}')
    
    model.eval()
    print(f"训练完成！最终损失: {loss.item():.6f}")
    
    # 6. 进行预测
    print("开始预测...")
    predictions = []
    real_values = []
    
    # 使用测试数据的前25个点初始化
    window_data = test_data[:window_size].copy()
    
    # 对测试数据进行预测
    for i in range(window_size, len(test_data)):
        # 标准化窗口数据
        normalized_window = scaler.transform(window_data.reshape(-1, 1)).flatten()
        
        # 准备输入
        input_seq = torch.FloatTensor(normalized_window).unsqueeze(0).unsqueeze(-1)
        
        # 预测
        with torch.no_grad():
            normalized_pred = model(input_seq).item()
        
        # 反标准化
        prediction = scaler.inverse_transform([[normalized_pred]])[0, 0]
        
        # 存储结果
        real_value = test_data[i]
        predictions.append(prediction)
        real_values.append(real_value)
        
        # 更新滑动窗口
        window_data = np.append(window_data[1:], real_value)
    
    # 7. 计算准确率
    predictions = np.array(predictions)
    real_values = np.array(real_values)
    
    print(f"\n预测结果:")
    print(f"预测点数: {len(predictions)}")
    
    # 计算误差
    errors = np.abs(real_values - predictions)
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean(errors**2))
    
    print(f"平均绝对误差: {mae:.6f} μm")
    print(f"均方根误差: {rmse:.6f} μm")
    print(f"最大误差: {np.max(errors):.6f} μm")
    print(f"最小误差: {np.min(errors):.6f} μm")
    
    # 计算相对误差准确率
    relative_errors = np.abs((real_values - predictions) / real_values) * 100
    
    accuracy_1percent = np.sum(relative_errors <= 1.0) / len(relative_errors) * 100
    accuracy_5percent = np.sum(relative_errors <= 5.0) / len(relative_errors) * 100
    accuracy_10percent = np.sum(relative_errors <= 10.0) / len(relative_errors) * 100
    accuracy_15percent = np.sum(relative_errors <= 15.0) / len(relative_errors) * 100
    accuracy_20percent = np.sum(relative_errors <= 20.0) / len(relative_errors) * 100
    
    print(f"\n准确率统计:")
    print(f"±1%准确率:  {accuracy_1percent:.1f}%")
    print(f"±5%准确率:  {accuracy_5percent:.1f}%")
    print(f"±10%准确率: {accuracy_10percent:.1f}%")
    print(f"±15%准确率: {accuracy_15percent:.1f}%")
    print(f"±20%准确率: {accuracy_20percent:.1f}%")
    
    # 8. 误差分布分析
    print(f"\n误差分布分析:")
    print(f"相对误差范围: {np.min(relative_errors):.2f}% - {np.max(relative_errors):.2f}%")
    print(f"相对误差均值: {np.mean(relative_errors):.2f}%")
    print(f"相对误差中位数: {np.median(relative_errors):.2f}%")
    print(f"相对误差标准差: {np.std(relative_errors):.2f}%")
    
    # 9. 详细分布统计
    print(f"\n详细分布统计:")
    bins = [0, 1, 2, 5, 10, 15, 20, 50, 100]
    for i in range(len(bins)-1):
        count = np.sum((relative_errors > bins[i]) & (relative_errors <= bins[i+1]))
        percentage = count / len(relative_errors) * 100
        print(f"{bins[i]}%-{bins[i+1]}%误差: {count}个点 ({percentage:.1f}%)")
    
    # 大于100%的误差
    count_large = np.sum(relative_errors > 100)
    percentage_large = count_large / len(relative_errors) * 100
    print(f">100%误差: {count_large}个点 ({percentage_large:.1f}%)")
    
    print(f"\n" + "="*60)
    print("✅ ±10%准确率计算完成！")
    print(f"🎯 关键结果: ±10%准确率 = {accuracy_10percent:.1f}%")
    print(f"📊 数据集: v1.txt(训练) + v2.txt(测试)")
    print("="*60)

if __name__ == "__main__":
    quick_accuracy_calculation()
