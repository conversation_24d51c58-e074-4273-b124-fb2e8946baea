"""
实时数据流处理演示
展示如何处理真实的实时振动数据流
"""

import asyncio
import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
from real_time_stream_processor import RealTimeStreamProcessor, SimulatedDataSource, RealTimeVisualizer
import threading

# 简化的LSTM模型（与displacement_real_time_demo.py保持一致）
class DisplacementLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

def train_model_for_real_time():
    """训练模型用于实时预测"""
    print("正在训练LSTM模型...")
    
    # 1. 加载训练数据
    try:
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values  # 位移列
        print(f"加载数据成功，数据点数: {len(displacement_data)}")
    except FileNotFoundError:
        print("错误: 未找到v1.txt文件")
        return None, None
    
    # 2. 数据预处理
    scaler = MinMaxScaler(feature_range=(-1, 1))
    displacement_normalized = scaler.fit_transform(displacement_data.reshape(-1, 1))
    displacement_normalized = torch.FloatTensor(displacement_normalized.flatten())
    
    # 3. 创建训练序列
    window_size = 25
    X, y = create_sequences(displacement_normalized.numpy(), window_size)
    
    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)
    
    # 4. 训练模型
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)
    
    epochs = 100
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()
        
        if epoch % 25 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')
    
    model.eval()
    print("模型训练完成！")
    
    return model, scaler

async def real_time_prediction_demo():
    """实时预测演示"""
    print("="*60)
    print("实时数据流处理演示")
    print("="*60)
    
    # 1. 训练模型
    model, scaler = train_model_for_real_time()
    if model is None:
        return
    
    # 2. 创建实时处理器
    processor = RealTimeStreamProcessor(
        model=model,
        scaler=scaler,
        window_size=25,
        buffer_size=10000
    )
    
    # 3. 准备历史数据用于初始化
    data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
    displacement_data = data.iloc[:, 1].values
    
    # 使用前100个点作为历史数据
    historical_data = displacement_data[:100]
    processor.initialize_with_historical_data(historical_data)
    
    # 4. 创建模拟数据源（从第101个点开始）
    data_source = SimulatedDataSource('v1.txt', sampling_rate=100)  # 100Hz采样率
    data_source.current_index = 100  # 从第101个点开始
    
    # 5. 定义预测回调函数
    prediction_results = []
    
    async def prediction_callback(real_value, prediction, timestamp):
        error = abs(real_value - prediction)
        prediction_results.append({
            'timestamp': timestamp,
            'real_value': real_value,
            'prediction': prediction,
            'error': error
        })
        
        # 每10个预测打印一次结果
        if len(prediction_results) % 10 == 0:
            avg_error = np.mean([r['error'] for r in prediction_results[-10:]])
            print(f"处理了 {len(prediction_results)} 个点, 最近10点平均误差: {avg_error:.6f} μm")
    
    # 6. 启动实时处理
    print("\n开始实时数据流处理...")
    print("处理参数:")
    print(f"  采样率: 100 Hz")
    print(f"  窗口大小: 25")
    print(f"  缓冲区大小: 10000")
    print(f"  异常值阈值: 3σ")
    
    # 创建处理任务
    processing_task = asyncio.create_task(
        processor.start_real_time_processing(data_source, prediction_callback)
    )
    
    # 运行一段时间后停止
    await asyncio.sleep(10)  # 运行10秒
    processor.stop_processing()
    
    # 等待处理任务完成
    try:
        await asyncio.wait_for(processing_task, timeout=2.0)
    except asyncio.TimeoutError:
        processing_task.cancel()
    
    # 7. 分析结果
    print("\n" + "="*60)
    print("实时处理结果分析")
    print("="*60)
    
    if prediction_results:
        errors = [r['error'] for r in prediction_results]
        real_values = [r['real_value'] for r in prediction_results]
        predictions = [r['prediction'] for r in prediction_results]
        
        print(f"总处理数据点: {len(prediction_results)}")
        print(f"平均误差: {np.mean(errors):.6f} μm")
        print(f"最大误差: {np.max(errors):.6f} μm")
        print(f"最小误差: {np.min(errors):.6f} μm")
        print(f"误差标准差: {np.std(errors):.6f} μm")
        
        # 计算相对误差
        relative_errors = [abs(r-p)/abs(r)*100 for r, p in zip(real_values, predictions) if abs(r) > 1e-8]
        if relative_errors:
            print(f"平均相对误差: {np.mean(relative_errors):.2f}%")
        
        # 性能统计
        stats = processor.get_performance_stats()
        print(f"\n性能统计:")
        print(f"  平均处理时间: {stats.get('avg_processing_time', 0)*1000:.2f} ms")
        print(f"  最大处理时间: {stats.get('max_processing_time', 0)*1000:.2f} ms")
        print(f"  处理速率: {stats.get('processing_rate', 0):.1f} 次/秒")
        print(f"  丢弃数据点: {stats.get('dropped_data_points', 0)}")
        print(f"  缓冲区利用率: {stats.get('buffer_utilization', 0)*100:.1f}%")

def run_with_visualization():
    """带可视化的实时处理演示"""
    print("启动带可视化的实时处理演示...")
    
    # 训练模型
    model, scaler = train_model_for_real_time()
    if model is None:
        return
    
    # 创建处理器
    processor = RealTimeStreamProcessor(model, scaler, window_size=25)
    
    # 初始化
    data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
    displacement_data = data.iloc[:, 1].values
    processor.initialize_with_historical_data(displacement_data[:100])
    
    # 创建数据源
    data_source = SimulatedDataSource('v1.txt', sampling_rate=50)  # 降低采样率便于观察
    data_source.current_index = 100
    
    # 创建可视化器
    visualizer = RealTimeVisualizer(processor, max_points=100)
    
    # 在后台线程中运行数据处理
    def background_processing():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        async def process_data():
            while data_source.is_connected():
                raw_data = await data_source.read_data()
                if raw_data is not None:
                    processor.process_single_point(raw_data)
                await asyncio.sleep(0.02)  # 50Hz
        
        loop.run_until_complete(process_data())
    
    # 启动后台处理
    processing_thread = threading.Thread(target=background_processing)
    processing_thread.daemon = True
    processing_thread.start()
    
    # 启动可视化
    print("启动实时可视化...")
    visualizer.start_animation(interval=100)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--visual":
        # 带可视化的演示
        run_with_visualization()
    else:
        # 基本演示
        asyncio.run(real_time_prediction_demo())
