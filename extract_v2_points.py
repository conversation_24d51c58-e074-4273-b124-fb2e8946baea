"""
从1_2.txt中平均取2000个点生成v2.txt
"""

import pandas as pd
import numpy as np

def extract_uniform_points_v2():
    """从1_2.txt中均匀提取2000个点生成v2.txt"""
    
    print("="*60)
    print("从1_2.txt提取2000个均匀分布点生成v2.txt")
    print("="*60)
    
    try:
        # 1. 读取原始数据
        print("正在读取1_2.txt...")
        data = pd.read_csv('1_2.txt', sep='\t', encoding='utf-8')
        
        print(f"原始数据信息:")
        print(f"  文件: 1_2.txt")
        print(f"  总数据点数: {len(data)}")
        print(f"  列名: {list(data.columns)}")
        
        # 检查数据范围
        time_col = data.iloc[:, 0]  # 时间列
        disp_col = data.iloc[:, 1]  # 位移列
        
        print(f"  时间范围: {time_col.min():.3f} - {time_col.max():.3f} ms")
        print(f"  位移范围: {disp_col.min():.6f} - {disp_col.max():.6f} μm")
        
        # 2. 计算均匀采样的索引
        target_points = 2000
        total_points = len(data)
        
        if total_points < target_points:
            print(f"警告: 原始数据点数({total_points})少于目标点数({target_points})")
            print("将使用所有数据点")
            selected_indices = np.arange(total_points)
        else:
            # 均匀采样
            selected_indices = np.linspace(0, total_points-1, target_points, dtype=int)
        
        print(f"\n采样策略:")
        print(f"  目标点数: {target_points}")
        print(f"  实际选取: {len(selected_indices)} 个点")
        print(f"  采样间隔: 每 {total_points/target_points:.1f} 个点取1个")
        
        # 3. 提取选定的数据点
        selected_data = data.iloc[selected_indices].copy()
        
        # 4. 重新编号时间（可选，保持原始时间或重新编号）
        # 这里保持原始时间
        print(f"\n提取后数据信息:")
        print(f"  数据点数: {len(selected_data)}")
        print(f"  时间范围: {selected_data.iloc[:, 0].min():.3f} - {selected_data.iloc[:, 0].max():.3f} ms")
        print(f"  位移范围: {selected_data.iloc[:, 1].min():.6f} - {selected_data.iloc[:, 1].max():.6f} μm")
        
        # 5. 保存为v2.txt
        print(f"\n正在保存到v2.txt...")
        selected_data.to_csv('v2.txt', sep='\t', index=False, encoding='utf-8')
        
        print("✅ v2.txt生成成功！")
        
        # 6. 验证生成的文件
        print(f"\n验证生成的文件:")
        verify_data = pd.read_csv('v2.txt', sep='\t', encoding='utf-8')
        print(f"  v2.txt数据点数: {len(verify_data)}")
        print(f"  前5行数据:")
        print(verify_data.head())
        
        # 7. 数据质量检查
        print(f"\n数据质量检查:")
        
        # 检查时间序列的单调性
        time_diff = np.diff(verify_data.iloc[:, 0])
        is_monotonic = np.all(time_diff > 0)
        print(f"  时间序列单调性: {'✅ 单调递增' if is_monotonic else '❌ 非单调'}")
        
        # 检查时间间隔
        avg_time_interval = np.mean(time_diff)
        print(f"  平均时间间隔: {avg_time_interval:.6f} ms")
        
        # 检查位移数据的统计特性
        disp_stats = verify_data.iloc[:, 1].describe()
        print(f"  位移统计:")
        print(f"    均值: {disp_stats['mean']:.6f} μm")
        print(f"    标准差: {disp_stats['std']:.6f} μm")
        print(f"    最小值: {disp_stats['min']:.6f} μm")
        print(f"    最大值: {disp_stats['max']:.6f} μm")
        
        # 8. 生成对比图表
        try:
            import matplotlib.pyplot as plt
            
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            
            # 原始数据概览（取前2000个点用于对比）
            original_sample = data.head(2000)
            ax1.plot(original_sample.iloc[:, 0], original_sample.iloc[:, 1], 'b-', alpha=0.7, linewidth=1)
            ax1.set_title('原始数据 (前2000个点)')
            ax1.set_xlabel('时间 (ms)')
            ax1.set_ylabel('位移 (μm)')
            ax1.grid(True, alpha=0.3)
            
            # 提取的数据
            ax2.plot(verify_data.iloc[:, 0], verify_data.iloc[:, 1], 'r-', alpha=0.8, linewidth=1)
            ax2.set_title('提取的数据 (均匀采样2000个点)')
            ax2.set_xlabel('时间 (ms)')
            ax2.set_ylabel('位移 (μm)')
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig('v2_extraction_comparison.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"  ✅ 对比图表已保存: v2_extraction_comparison.png")
            
        except ImportError:
            print(f"  ⚠️ matplotlib未安装，跳过图表生成")
        except Exception as e:
            print(f"  ⚠️ 图表生成失败: {e}")
        
        print(f"\n" + "="*60)
        print("v2.txt生成完成！")
        print("="*60)
        print(f"📁 输出文件:")
        print(f"  - v2.txt (2000个数据点)")
        print(f"  - v2_extraction_comparison.png (对比图表)")
        print(f"\n📊 数据摘要:")
        print(f"  原始数据: {len(data)} 个点")
        print(f"  提取数据: {len(verify_data)} 个点")
        print(f"  采样比例: {len(verify_data)/len(data)*100:.2f}%")
        
        return True
        
    except FileNotFoundError:
        print("❌ 错误: 未找到1_2.txt文件")
        print("请确保1_2.txt文件在当前目录中")
        return False
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = extract_uniform_points_v2()
    if success:
        print("\n🎉 任务完成！v2.txt已成功生成")
    else:
        print("\n❌ 任务失败，请检查错误信息")
