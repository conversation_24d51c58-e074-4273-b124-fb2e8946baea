"""
基于三个预测文件生成对比视频
- v2_lstm.txt: LSTM预测结果
- v2_akf.txt: LSTM+AKF预测结果  
- v2_lstm_kf_1000points.txt: LSTM+KF预测结果
使用后1000个点生成预测对比视频
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体显示
import matplotlib
from matplotlib.font_manager import FontProperties

# 设置中文字体
def setup_chinese_font():
    """设置中文字体"""
    try:
        # 尝试多种中文字体
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'STHeiti', 'WenQuanYi Micro Hei']

        for font_name in chinese_fonts:
            try:
                # 测试字体是否可用
                font_prop = FontProperties(fname=None, family=font_name)
                fig_test, ax_test = plt.subplots(figsize=(1, 1))
                ax_test.text(0.5, 0.5, '测试中文', fontproperties=font_prop)
                plt.close(fig_test)

                # 如果成功，设置为默认字体
                plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
                plt.rcParams['axes.unicode_minus'] = False
                print(f"✅ 中文字体设置成功: {font_name}")
                return font_prop
            except:
                continue

        # 如果所有字体都失败，使用系统默认
        print("⚠️ 未找到合适的中文字体，使用系统默认字体")
        plt.rcParams['axes.unicode_minus'] = False
        return None

    except Exception as e:
        print(f"⚠️ 字体设置失败: {e}")
        plt.rcParams['axes.unicode_minus'] = False
        return None

# 初始化字体
chinese_font = setup_chinese_font()

def load_prediction_data():
    """加载三个预测文件的数据"""
    print("📊 加载预测数据文件...")
    
    try:
        # 加载原始数据
        v2_data = pd.read_csv('v2.txt', sep='\t', encoding='utf-8')
        original_data = v2_data.iloc[:, 1].values
        original_times = v2_data.iloc[:, 0].values
        
        # 加载LSTM预测结果
        lstm_data = pd.read_csv('v2_lstm.txt', sep='\t', encoding='utf-8')
        lstm_predictions = lstm_data.iloc[:, 1].values
        lstm_times = lstm_data.iloc[:, 0].values
        
        # 加载LSTM+AKF预测结果
        akf_data = pd.read_csv('v2_akf.txt', sep='\t', encoding='utf-8')
        akf_predictions = akf_data.iloc[:, 1].values
        akf_times = akf_data.iloc[:, 0].values
        
        # 加载LSTM+KF预测结果
        lstm_kf_data = pd.read_csv('v2_lstm_kf_1000points.txt', sep='\t', encoding='utf-8')
        lstm_kf_predictions = lstm_kf_data.iloc[:, 1].values
        lstm_kf_times = lstm_kf_data.iloc[:, 0].values
        
        print(f"✅ 成功加载数据:")
        print(f"   - 原始数据: {len(original_data)} 点")
        print(f"   - LSTM预测: {len(lstm_predictions)} 点")
        print(f"   - LSTM+AKF预测: {len(akf_predictions)} 点")
        print(f"   - LSTM+KF预测: {len(lstm_kf_predictions)} 点")
        
        # 获取LSTM+KF的时间范围
        lstm_kf_start_time = lstm_kf_times[0]
        lstm_kf_end_time = lstm_kf_times[-1]
        
        print(f"LSTM+KF时间范围: {lstm_kf_start_time:.1f} - {lstm_kf_end_time:.1f} ms")
        
        # 在原始数据中找到对应的时间段
        original_mask = (original_times >= lstm_kf_start_time) & (original_times <= lstm_kf_end_time)
        lstm_mask = (lstm_times >= lstm_kf_start_time) & (lstm_times <= lstm_kf_end_time)
        akf_mask = (akf_times >= lstm_kf_start_time) & (akf_times <= lstm_kf_end_time)
        
        # 提取对应时间段的数据
        original_segment = original_data[original_mask]
        original_times_segment = original_times[original_mask]
        
        lstm_segment = lstm_predictions[lstm_mask]
        lstm_times_segment = lstm_times[lstm_mask]
        
        akf_segment = akf_predictions[akf_mask]
        akf_times_segment = akf_times[akf_mask]
        
        # 取后1000个点
        num_points = min(1000, len(lstm_kf_predictions), len(original_segment), 
                        len(lstm_segment), len(akf_segment))
        
        # 从末尾取1000个点
        original_final = original_segment[-num_points:]
        original_times_final = original_times_segment[-num_points:]
        
        lstm_final = lstm_segment[-num_points:]
        lstm_times_final = lstm_times_segment[-num_points:]
        
        akf_final = akf_segment[-num_points:]
        akf_times_final = akf_times_segment[-num_points:]
        
        lstm_kf_final = lstm_kf_predictions[-num_points:]
        lstm_kf_times_final = lstm_kf_times[-num_points:]
        
        print(f"✅ 提取后{num_points}个点的数据:")
        print(f"   - 时间范围: {original_times_final[0]:.1f} - {original_times_final[-1]:.1f} ms")
        
        # 计算性能指标
        lstm_mae = np.mean(np.abs(original_final - lstm_final))
        akf_mae = np.mean(np.abs(original_final - akf_final))
        lstm_kf_mae = np.mean(np.abs(original_final - lstm_kf_final))
        
        print(f"📈 预测性能指标 (MAE):")
        print(f"   - LSTM: {lstm_mae:.6f} μm")
        print(f"   - LSTM+AKF: {akf_mae:.6f} μm")
        print(f"   - LSTM+KF: {lstm_kf_mae:.6f} μm")
        
        return {
            'original': original_final,
            'lstm': lstm_final,
            'akf': akf_final,
            'lstm_kf': lstm_kf_final,
            'times': original_times_final,
            'mae_scores': {
                'lstm': lstm_mae,
                'akf': akf_mae,
                'lstm_kf': lstm_kf_mae
            }
        }
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def create_prediction_video(data):
    """创建预测对比动态视频"""
    print("🎬 开始生成预测对比视频...")
    
    original = data['original']
    lstm_pred = data['lstm']
    akf_pred = data['akf']
    lstm_kf_pred = data['lstm_kf']
    times = data['times']
    mae_scores = data['mae_scores']
    
    # 创建时间轴（相对时间）
    time_points = np.arange(len(original))
    
    # 设置动画参数
    window_size = 100  # 显示窗口大小
    step_size = 2      # 每帧前进的步数
    total_frames = (len(original) - window_size) // step_size
    
    print(f"   - 数据点数: {len(original)}")
    print(f"   - 窗口大小: {window_size} 点")
    print(f"   - 步进大小: {step_size} 点")
    print(f"   - 总帧数: {total_frames}")
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))

    # 设置标题（使用中文字体）
    if chinese_font:
        fig.suptitle('AFM振动预测实时对比 - 三种方法后1000点对比', fontsize=16, fontweight='bold',
                     fontproperties=chinese_font)
    else:
        fig.suptitle('AFM Vibration Prediction Real-time Comparison - Last 1000 Points',
                     fontsize=16, fontweight='bold')
    
    # 初始化累积误差
    cumulative_lstm_mae = []
    cumulative_akf_mae = []
    cumulative_lstm_kf_mae = []
    
    def animate(frame):
        # 清除之前的图形
        ax1.clear()
        ax2.clear()
        
        # 计算当前窗口
        start_idx = frame * step_size
        end_idx = start_idx + window_size
        
        if end_idx > len(original):
            end_idx = len(original)
            start_idx = end_idx - window_size
        
        # 当前窗口的数据
        current_time = time_points[start_idx:end_idx]
        current_original = original[start_idx:end_idx]
        current_lstm = lstm_pred[start_idx:end_idx]
        current_akf = akf_pred[start_idx:end_idx]
        current_lstm_kf = lstm_kf_pred[start_idx:end_idx]
        
        # 计算累积误差
        lstm_mae = np.mean(np.abs(original[:end_idx] - lstm_pred[:end_idx]))
        akf_mae = np.mean(np.abs(original[:end_idx] - akf_pred[:end_idx]))
        lstm_kf_mae = np.mean(np.abs(original[:end_idx] - lstm_kf_pred[:end_idx]))
        
        cumulative_lstm_mae.append(lstm_mae)
        cumulative_akf_mae.append(akf_mae)
        cumulative_lstm_kf_mae.append(lstm_kf_mae)
        
        # 第一个子图：实时预测对比
        if chinese_font:
            ax1.plot(current_time, current_original, 'k-', linewidth=2.5, label='真实值', alpha=0.9)
            ax1.plot(current_time, current_lstm, 'b--', linewidth=2, label='LSTM预测', alpha=0.8)
            ax1.plot(current_time, current_akf, 'r:', linewidth=2, label='LSTM+AKF预测', alpha=0.8)
            ax1.plot(current_time, current_lstm_kf, 'g-.', linewidth=2, label='LSTM+KF预测', alpha=0.8)

            ax1.set_xlabel('时间步', fontproperties=chinese_font)
            ax1.set_ylabel('位移 (μm)', fontproperties=chinese_font)
            ax1.set_title(f'实时预测对比 (第{frame+1}/{total_frames}帧)', fontproperties=chinese_font)
        else:
            ax1.plot(current_time, current_original, 'k-', linewidth=2.5, label='Real Value', alpha=0.9)
            ax1.plot(current_time, current_lstm, 'b--', linewidth=2, label='LSTM Prediction', alpha=0.8)
            ax1.plot(current_time, current_akf, 'r:', linewidth=2, label='LSTM+AKF Prediction', alpha=0.8)
            ax1.plot(current_time, current_lstm_kf, 'g-.', linewidth=2, label='LSTM+KF Prediction', alpha=0.8)

            ax1.set_xlabel('Time Step')
            ax1.set_ylabel('Displacement (μm)')
            ax1.set_title(f'Real-time Prediction Comparison (Frame {frame+1}/{total_frames})')

        ax1.legend(loc='upper right', prop=chinese_font if chinese_font else None)
        ax1.grid(True, alpha=0.3)
        
        # 添加当前误差信息
        if chinese_font:
            error_text = f'当前MAE:\nLSTM: {lstm_mae:.6f} μm\nLSTM+AKF: {akf_mae:.6f} μm\nLSTM+KF: {lstm_kf_mae:.6f} μm'
        else:
            error_text = f'Current MAE:\nLSTM: {lstm_mae:.6f} μm\nLSTM+AKF: {akf_mae:.6f} μm\nLSTM+KF: {lstm_kf_mae:.6f} μm'

        ax1.text(0.02, 0.98, error_text,
                transform=ax1.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                fontsize=10, fontproperties=chinese_font if chinese_font else None)

        # 第二个子图：累积误差曲线
        error_frames = range(1, len(cumulative_lstm_mae) + 1)

        if chinese_font:
            ax2.plot(error_frames, cumulative_lstm_mae, 'b-', linewidth=2, label='LSTM累积MAE', alpha=0.8)
            ax2.plot(error_frames, cumulative_akf_mae, 'r-', linewidth=2, label='LSTM+AKF累积MAE', alpha=0.8)
            ax2.plot(error_frames, cumulative_lstm_kf_mae, 'g-', linewidth=2, label='LSTM+KF累积MAE', alpha=0.8)

            ax2.set_xlabel('帧数', fontproperties=chinese_font)
            ax2.set_ylabel('累积平均绝对误差 (μm)', fontproperties=chinese_font)
            ax2.set_title('累积误差变化曲线', fontproperties=chinese_font)
        else:
            ax2.plot(error_frames, cumulative_lstm_mae, 'b-', linewidth=2, label='LSTM Cumulative MAE', alpha=0.8)
            ax2.plot(error_frames, cumulative_akf_mae, 'r-', linewidth=2, label='LSTM+AKF Cumulative MAE', alpha=0.8)
            ax2.plot(error_frames, cumulative_lstm_kf_mae, 'g-', linewidth=2, label='LSTM+KF Cumulative MAE', alpha=0.8)

            ax2.set_xlabel('Frame Number')
            ax2.set_ylabel('Cumulative Mean Absolute Error (μm)')
            ax2.set_title('Cumulative Error Curve')

        ax2.legend(prop=chinese_font if chinese_font else None)
        ax2.grid(True, alpha=0.3)
        ax2.set_yscale('log')  # 使用对数坐标

        # 添加改进百分比
        if lstm_mae > 0:
            akf_improvement = ((lstm_mae - akf_mae) / lstm_mae * 100)
            lstm_kf_improvement = ((lstm_mae - lstm_kf_mae) / lstm_mae * 100)

            if chinese_font:
                improvement_text = f'相对LSTM改进:\nLSTM+AKF: {akf_improvement:.2f}%\nLSTM+KF: {lstm_kf_improvement:.2f}%'
            else:
                improvement_text = f'Improvement vs LSTM:\nLSTM+AKF: {akf_improvement:.2f}%\nLSTM+KF: {lstm_kf_improvement:.2f}%'

            ax2.text(0.02, 0.98, improvement_text,
                    transform=ax2.transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8),
                    fontsize=10, fontproperties=chinese_font if chinese_font else None)
        
        plt.tight_layout()
    
    # 创建动画
    print("🎥 正在渲染动画...")
    anim = animation.FuncAnimation(fig, animate, frames=total_frames, 
                                 interval=150, blit=False, repeat=True)
    
    return anim, fig

def save_video(anim, fig):
    """保存视频文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    try:
        print("💾 保存GIF文件...")
        gif_filename = f'afm_three_predictions_video_{timestamp}.gif'
        anim.save(gif_filename, writer='pillow', fps=8, dpi=100)
        print(f"✅ GIF已保存: {gif_filename}")
        
        # 尝试保存MP4
        try:
            print("💾 保存MP4文件...")
            mp4_filename = f'afm_three_predictions_video_{timestamp}.mp4'
            Writer = animation.writers['ffmpeg']
            writer = Writer(fps=10, metadata=dict(artist='AFM Prediction System'), bitrate=1800)
            anim.save(mp4_filename, writer=writer, dpi=100)
            print(f"✅ MP4已保存: {mp4_filename}")
        except Exception as e:
            print(f"⚠️ MP4保存失败: {e}")
            
        return gif_filename
        
    except Exception as e:
        print(f"❌ 视频保存失败: {e}")
        return None

if __name__ == "__main__":
    print("🎬 AFM三种预测方法对比视频生成器")
    print("=" * 60)
    print("数据源:")
    print("  - v2_lstm.txt: LSTM预测结果")
    print("  - v2_akf.txt: LSTM+AKF预测结果")
    print("  - v2_lstm_kf_1000points.txt: LSTM+KF预测结果")
    print("=" * 60)
    
    # 加载数据
    data = load_prediction_data()
    
    if data is None:
        print("❌ 数据加载失败，请检查文件是否存在")
        exit(1)
    
    # 生成视频
    anim, fig = create_prediction_video(data)
    
    # 保存视频
    output_file = save_video(anim, fig)
    
    if output_file:
        print(f"\n🎉 视频生成完成！")
        print(f"📁 输出文件: {output_file}")
        print("\n📊 最终性能对比:")
        mae_scores = data['mae_scores']
        print(f"   - LSTM MAE: {mae_scores['lstm']:.6f} μm")
        print(f"   - LSTM+AKF MAE: {mae_scores['akf']:.6f} μm")
        print(f"   - LSTM+KF MAE: {mae_scores['lstm_kf']:.6f} μm")
        
        # 计算改进幅度
        lstm_mae = mae_scores['lstm']
        if lstm_mae > 0:
            akf_improvement = ((lstm_mae - mae_scores['akf']) / lstm_mae * 100)
            lstm_kf_improvement = ((lstm_mae - mae_scores['lstm_kf']) / lstm_mae * 100)
            print(f"\n📈 相对LSTM的改进幅度:")
            print(f"   - LSTM+AKF: {akf_improvement:.2f}%")
            print(f"   - LSTM+KF: {lstm_kf_improvement:.2f}%")
        
        # 显示图形（可选）
        try:
            plt.show()
        except:
            print("💡 如需查看动画，请运行生成的视频文件")
    else:
        print("❌ 视频生成失败")
