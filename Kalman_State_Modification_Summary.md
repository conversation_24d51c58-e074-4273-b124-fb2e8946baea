# 卡尔曼滤波器状态向量修改总结

## 修改概述

将集成LSTM-卡尔曼滤波器的状态向量从 **[位移, 速度]** 改为 **[位移, 加速度]**，以更好地捕捉振动信号的动态特性。

## 主要修改内容

### 1. 状态向量变更

**原状态向量：**
```python
# 状态向量 [位移, 速度]
self.x = np.array([0.0, 0.0])  # [位移, 速度]

# 状态转移矩阵A (2x2)
# 位移(k+1) = 位移(k) + 速度(k)
# 速度(k+1) = 速度(k)
self.A = np.array([[1.0, 1.0],
                  [0.0, 1.0]])
```

**新状态向量：**
```python
# 状态向量 [位移, 加速度]
self.x = np.array([0.0, 0.0])  # [位移, 加速度]

# 状态转移矩阵A (2x2)
# 位移(k+1) = 位移(k) + 0.5 * 加速度(k) * dt^2
# 加速度(k+1) = 加速度(k)
dt = 1.0  # 时间步长
self.A = np.array([[1.0, 0.5 * dt * dt],
                  [0.0, 1.0]])
```

### 2. 运动学模型

#### 新的状态转移方程
基于经典运动学方程，假设时间步长 dt=1：

- **位移更新**：`位移(k+1) = 位移(k) + 0.5 * 加速度(k) * dt²`
- **加速度更新**：`加速度(k+1) = 加速度(k)` （假设短时间内相对稳定）

#### 物理意义
- **位移**：直接观测量，表示振动的瞬时位置
- **加速度**：二阶导数，更好地反映振动的动态变化趋势

### 3. 初始化改进

#### 加速度初始值估计
```python
def initialize_buffer(self, initial_data):
    # 估计初始加速度（基于最近几个数据点）
    if len(initial_data) >= 3:
        # 使用最后3个点估计加速度
        recent_displacements = initial_data[-3:]
        # 计算速度：v = (x[i] - x[i-1]) / dt
        v1 = recent_displacements[1] - recent_displacements[0]
        v2 = recent_displacements[2] - recent_displacements[1]
        # 计算加速度：a = (v[i] - v[i-1]) / dt
        initial_acceleration = v2 - v1
        self.x[1] = initial_acceleration
    else:
        self.x[1] = 0.0  # 初始加速度为0
```

### 4. 加速度动态更新

#### 新增加速度估计函数
```python
def _update_acceleration_estimate(self, current_displacement):
    """更新加速度估计（基于观测到的位移变化）"""
    if len(self.measurement_history) >= 2:
        # 计算当前速度
        current_velocity = current_displacement - self.previous_displacement
        
        # 计算加速度变化
        acceleration_change = current_velocity - self.previous_velocity
        
        # 使用指数平滑更新加速度估计
        alpha = 0.3  # 平滑系数
        self.x[1] = (1 - alpha) * self.x[1] + alpha * acceleration_change
        
        # 更新历史值
        self.previous_velocity = current_velocity
        
    self.previous_displacement = current_displacement
```

### 5. 自适应噪声调整

#### 增强的自适应调整
```python
def _adaptive_adjustment(self):
    # 计算加速度变化程度（二阶差分）
    if len(recent_measurements) >= 3:
        acceleration_changes = np.abs(np.diff(displacement_changes))
        avg_acceleration_change = np.mean(acceleration_changes)
    
    # 检测变化模式
    if max_change > threshold * 2 or avg_acceleration_change > threshold:
        # 检测到较大变化或加速度变化，适度增加过程噪声
        self.Q[0, 0] = min(5e-3, self.Q[0, 0] * 1.2)  # 位移过程噪声
        self.Q[1, 1] = min(1e-2, self.Q[1, 1] * 1.3)  # 加速度过程噪声
```

### 6. 过程噪声协方差调整

#### 针对位移+加速度的噪声模型
```python
# 过程噪声协方差矩阵Q (2x2)
# 位移的过程噪声相对较小，加速度的过程噪声可以稍大一些
self.Q = np.diag([initial_process_variance, initial_process_variance * 2.0])
```

### 7. 技术优势

#### 位移+加速度状态的优势

1. **更好的动态响应**：
   - 加速度是位移的二阶导数，能更敏感地捕捉变化趋势
   - 对振动信号的突变和频率变化响应更快

2. **物理意义更明确**：
   - 位移：直接观测量
   - 加速度：反映振动的动力学特性

3. **预测精度提升**：
   - 加速度信息有助于更准确地预测位移的未来变化
   - 特别适合处理非线性和时变的振动信号

4. **自适应能力增强**：
   - 基于加速度变化的自适应噪声调整
   - 更好地处理不同振动模式的切换

### 8. 测试验证结果

#### 测试输出示例
```
初始状态: 位移=-0.092725, 加速度=0.072251
步骤 1: 真实值=-0.126958, 预测值=-0.100449, 误差=0.026509
        状态: 位移=-0.100449, 加速度=0.072251
步骤 2: 真实值=-0.091481, 预测值=-0.110970, 误差=0.019489
        状态: 位移=-0.110970, 加速度=0.059001
```

#### 验证结果
- ✅ 状态向量正确更新为[位移, 加速度]
- ✅ 加速度值在合理范围内动态变化
- ✅ 预测精度保持良好水平
- ✅ 系统稳定性良好

### 9. 文件更新

#### 主要文件修改
1. **displacement_real_time_demo.py**：
   - 更新 `IntegratedLSTMKalmanFilter` 类
   - 修改状态转移矩阵和初始化逻辑
   - 增加加速度估计和更新函数
   - 更新可视化标题和输出文件名

2. **test_integrated_lstm_kalman.py**：
   - 更新测试输出信息
   - 增加状态向量显示

#### 输出文件名更新
- 图像：`integrated_lstm_kalman_disp_accel_prediction.png`
- 数据：`integrated_lstm_kalman_disp_accel_results.csv`

### 10. 使用方法

#### 运行演示
```bash
python displacement_real_time_demo.py
```

#### 运行测试
```bash
python test_integrated_lstm_kalman.py
```

## 总结

成功将卡尔曼滤波器的状态向量从[位移, 速度]改为[位移, 加速度]，新的状态模型：

- **更好地反映振动信号的动态特性**
- **提供更敏感的变化检测能力**
- **保持了系统的稳定性和预测精度**
- **增强了对不同振动模式的自适应能力**

这种改进特别适合处理具有复杂动态特性的振动位移信号，能够更准确地捕捉和预测振动的变化趋势。
