"""
从vib1.txt中均匀取2000个点，另存为v1.txt
"""

import pandas as pd
import numpy as np

def extract_uniform_points(input_file='vib1.txt', output_file='v1.txt', num_points=2000):
    """
    从输入文件中均匀提取指定数量的数据点
    
    参数:
    - input_file: 输入文件名
    - output_file: 输出文件名
    - num_points: 要提取的点数
    """
    try:
        # 读取原始数据
        print(f"正在读取文件: {input_file}")
        data = pd.read_csv(input_file, sep='\t', encoding='utf-8')
        
        print(f"原始数据信息:")
        print(f"  总数据点数: {len(data)}")
        print(f"  列名: {list(data.columns)}")
        
        # 检查数据量
        total_points = len(data)
        if num_points > total_points:
            print(f"警告: 要提取的点数({num_points})大于总数据点数({total_points})")
            print(f"将提取所有{total_points}个点")
            num_points = total_points
        
        # 计算均匀采样的索引
        if num_points == total_points:
            # 如果要提取所有点，直接使用所有索引
            indices = np.arange(total_points)
        else:
            # 均匀采样：在0到total_points-1之间均匀选择num_points个点
            indices = np.linspace(0, total_points-1, num_points, dtype=int)
        
        # 提取数据
        extracted_data = data.iloc[indices].copy()
        
        # 重置索引（可选）
        extracted_data.reset_index(drop=True, inplace=True)
        
        print(f"\n均匀采样信息:")
        print(f"  提取点数: {len(extracted_data)}")
        print(f"  采样间隔: 每{total_points/num_points:.2f}个点取1个")
        print(f"  第一个点索引: {indices[0]}")
        print(f"  最后一个点索引: {indices[-1]}")
        
        # 显示提取的数据范围
        time_column = data.columns[0]  # 时间列
        displacement_column = data.columns[1]  # 位移列
        
        print(f"\n提取数据的范围:")
        print(f"  时间范围: {extracted_data[time_column].min():.3f} ~ {extracted_data[time_column].max():.3f} ms")
        print(f"  位移范围: {extracted_data[displacement_column].min():.6f} ~ {extracted_data[displacement_column].max():.6f} μm")
        print(f"  位移标准差: {extracted_data[displacement_column].std():.6f} μm")
        
        # 保存到新文件
        print(f"\n正在保存到文件: {output_file}")
        extracted_data.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
        
        print(f"✅ 成功完成！")
        print(f"   从 {input_file} 中均匀提取了 {len(extracted_data)} 个点")
        print(f"   已保存到 {output_file}")
        
        # 验证保存的文件
        print(f"\n验证保存的文件:")
        verification_data = pd.read_csv(output_file, sep='\t', encoding='utf-8')
        print(f"  保存文件的数据点数: {len(verification_data)}")
        print(f"  保存文件的列名: {list(verification_data.columns)}")
        
        return extracted_data
        
    except FileNotFoundError:
        print(f"❌ 错误: 未找到文件 {input_file}")
        return None
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")
        return None

def show_sampling_pattern(input_file='vib1.txt', num_points=2000, show_first_n=10):
    """
    显示采样模式的前几个点
    """
    try:
        data = pd.read_csv(input_file, sep='\t', encoding='utf-8')
        total_points = len(data)
        
        if num_points > total_points:
            num_points = total_points
            
        indices = np.linspace(0, total_points-1, num_points, dtype=int)
        
        print(f"\n采样模式示例 (前{show_first_n}个点):")
        print("采样点序号    原始数据索引    时间[ms]        位移[μm]")
        print("-" * 65)
        
        time_column = data.columns[0]
        displacement_column = data.columns[1]
        
        for i in range(min(show_first_n, len(indices))):
            idx = indices[i]
            time_val = data.iloc[idx][time_column]
            disp_val = data.iloc[idx][displacement_column]
            print(f"{i+1:>8}        {idx:>8}        {time_val:>8.3f}      {disp_val:>10.6f}")
            
        if len(indices) > show_first_n:
            print("...")
            # 显示最后几个点
            print(f"最后一个点:")
            idx = indices[-1]
            time_val = data.iloc[idx][time_column]
            disp_val = data.iloc[idx][displacement_column]
            print(f"{len(indices):>8}        {idx:>8}        {time_val:>8.3f}      {disp_val:>10.6f}")
            
    except Exception as e:
        print(f"显示采样模式时出错: {e}")

def main():
    """主函数"""
    print("="*60)
    print("从vib1.txt中均匀提取2000个点并保存为v1.txt")
    print("="*60)
    
    # 显示采样模式
    show_sampling_pattern('vib1.txt', 2000, 10)
    
    # 执行提取
    result = extract_uniform_points('vib1.txt', 'v1.txt', 2000)
    
    if result is not None:
        print(f"\n📊 数据统计对比:")
        
        # 读取原始数据进行对比
        try:
            original_data = pd.read_csv('vib1.txt', sep='\t', encoding='utf-8')
            displacement_col = original_data.columns[1]
            
            print(f"原始数据 (vib1.txt):")
            print(f"  数据点数: {len(original_data)}")
            print(f"  位移均值: {original_data[displacement_col].mean():.6f} μm")
            print(f"  位移标准差: {original_data[displacement_col].std():.6f} μm")
            print(f"  位移范围: {original_data[displacement_col].min():.6f} ~ {original_data[displacement_col].max():.6f} μm")
            
            print(f"\n提取数据 (v1.txt):")
            print(f"  数据点数: {len(result)}")
            print(f"  位移均值: {result[displacement_col].mean():.6f} μm")
            print(f"  位移标准差: {result[displacement_col].std():.6f} μm")
            print(f"  位移范围: {result[displacement_col].min():.6f} ~ {result[displacement_col].max():.6f} μm")
            
            # 计算统计特征的保持程度
            mean_diff = abs(original_data[displacement_col].mean() - result[displacement_col].mean())
            std_diff = abs(original_data[displacement_col].std() - result[displacement_col].std())
            
            print(f"\n📈 统计特征保持情况:")
            print(f"  均值差异: {mean_diff:.8f} μm")
            print(f"  标准差差异: {std_diff:.8f} μm")
            print(f"  数据压缩比: {len(result)/len(original_data)*100:.2f}%")
            
        except Exception as e:
            print(f"对比分析时出错: {e}")

if __name__ == "__main__":
    main()
