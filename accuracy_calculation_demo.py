"""
预测平均准确率计算方法演示
基于v1.txt振动预测结果
"""

import pandas as pd
import numpy as np

def calculate_comprehensive_accuracy(csv_file='v1_improved_integrated_lstm_kalman_results.csv'):
    """
    计算综合准确率指标
    """
    print("="*60)
    print("预测平均准确率计算方法演示")
    print("="*60)
    
    try:
        # 读取预测结果
        data = pd.read_csv(csv_file)
        print(f"✅ 成功读取预测结果: {len(data)} 个数据点")
        
        # 提取数据
        y_true = data['real_displacement'].values  # 真实值
        y_pred_lstm = data['lstm_prediction'].values  # LSTM预测值
        y_pred_kalman = data['improved_integrated_kalman_prediction'].values  # 集成预测值
        
        print(f"\n📊 数据概览:")
        print(f"  真实值范围: {y_true.min():.6f} ~ {y_true.max():.6f} μm")
        print(f"  LSTM预测范围: {y_pred_lstm.min():.6f} ~ {y_pred_lstm.max():.6f} μm")
        print(f"  集成预测范围: {y_pred_kalman.min():.6f} ~ {y_pred_kalman.max():.6f} μm")
        
        # 1. 基本误差指标
        print(f"\n🎯 1. 基本误差指标")
        
        # MAE (平均绝对误差)
        mae_lstm = np.mean(np.abs(y_true - y_pred_lstm))
        mae_kalman = np.mean(np.abs(y_true - y_pred_kalman))
        print(f"  平均绝对误差 (MAE):")
        print(f"    LSTM: {mae_lstm:.6f} μm")
        print(f"    集成: {mae_kalman:.6f} μm")
        print(f"    改善: {((mae_lstm - mae_kalman) / mae_lstm * 100):.2f}%")
        
        # RMSE (均方根误差)
        rmse_lstm = np.sqrt(np.mean((y_true - y_pred_lstm) ** 2))
        rmse_kalman = np.sqrt(np.mean((y_true - y_pred_kalman) ** 2))
        print(f"  均方根误差 (RMSE):")
        print(f"    LSTM: {rmse_lstm:.6f} μm")
        print(f"    集成: {rmse_kalman:.6f} μm")
        print(f"    改善: {((rmse_lstm - rmse_kalman) / rmse_lstm * 100):.2f}%")
        
        # 2. 相对误差准确率
        print(f"\n📈 2. 相对误差准确率")
        
        # 避免除零错误
        non_zero_mask = np.abs(y_true) > 1e-15
        if np.sum(non_zero_mask) > 0:
            # MAPE (平均绝对百分比误差)
            mape_lstm = np.mean(np.abs((y_true[non_zero_mask] - y_pred_lstm[non_zero_mask]) / y_true[non_zero_mask])) * 100
            mape_kalman = np.mean(np.abs((y_true[non_zero_mask] - y_pred_kalman[non_zero_mask]) / y_true[non_zero_mask])) * 100
            print(f"  平均绝对百分比误差 (MAPE):")
            print(f"    LSTM: {mape_lstm:.2f}%")
            print(f"    集成: {mape_kalman:.2f}%")
            print(f"    改善: {((mape_lstm - mape_kalman) / mape_lstm * 100):.2f}%")
            
            # 阈值准确率
            rel_errors_lstm = np.abs((y_true[non_zero_mask] - y_pred_lstm[non_zero_mask]) / y_true[non_zero_mask]) * 100
            rel_errors_kalman = np.abs((y_true[non_zero_mask] - y_pred_kalman[non_zero_mask]) / y_true[non_zero_mask]) * 100
            
            # ±1%准确率
            acc_1_lstm = np.sum(rel_errors_lstm <= 1.0) / len(rel_errors_lstm) * 100
            acc_1_kalman = np.sum(rel_errors_kalman <= 1.0) / len(rel_errors_kalman) * 100
            print(f"  ±1%以内准确率:")
            print(f"    LSTM: {acc_1_lstm:.2f}%")
            print(f"    集成: {acc_1_kalman:.2f}%")
            print(f"    改善: +{acc_1_kalman - acc_1_lstm:.2f}%")
            
            # ±5%准确率
            acc_5_lstm = np.sum(rel_errors_lstm <= 5.0) / len(rel_errors_lstm) * 100
            acc_5_kalman = np.sum(rel_errors_kalman <= 5.0) / len(rel_errors_kalman) * 100
            print(f"  ±5%以内准确率:")
            print(f"    LSTM: {acc_5_lstm:.2f}%")
            print(f"    集成: {acc_5_kalman:.2f}%")
            print(f"    改善: +{acc_5_kalman - acc_5_lstm:.2f}%")
            
            # ±10%准确率
            acc_10_lstm = np.sum(rel_errors_lstm <= 10.0) / len(rel_errors_lstm) * 100
            acc_10_kalman = np.sum(rel_errors_kalman <= 10.0) / len(rel_errors_kalman) * 100
            print(f"  ±10%以内准确率:")
            print(f"    LSTM: {acc_10_lstm:.2f}%")
            print(f"    集成: {acc_10_kalman:.2f}%")
            print(f"    改善: +{acc_10_kalman - acc_10_lstm:.2f}%")
        
        # 3. 相关性指标
        print(f"\n🔗 3. 相关性指标")
        
        # R²决定系数
        ss_res_lstm = np.sum((y_true - y_pred_lstm) ** 2)
        ss_res_kalman = np.sum((y_true - y_pred_kalman) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        
        r2_lstm = 1 - (ss_res_lstm / ss_tot) if ss_tot != 0 else 1.0
        r2_kalman = 1 - (ss_res_kalman / ss_tot) if ss_tot != 0 else 1.0
        print(f"  决定系数 (R²):")
        print(f"    LSTM: {r2_lstm:.6f}")
        print(f"    集成: {r2_kalman:.6f}")
        print(f"    改善: +{r2_kalman - r2_lstm:.6f}")
        
        # 相关系数
        corr_lstm = np.corrcoef(y_true, y_pred_lstm)[0, 1] if len(y_true) > 1 else 1.0
        corr_kalman = np.corrcoef(y_true, y_pred_kalman)[0, 1] if len(y_true) > 1 else 1.0
        print(f"  相关系数:")
        print(f"    LSTM: {corr_lstm:.6f}")
        print(f"    集成: {corr_kalman:.6f}")
        print(f"    改善: +{corr_kalman - corr_lstm:.6f}")
        
        # 4. 综合准确率评分
        print(f"\n⭐ 4. 综合准确率评分")
        
        # 基于多个指标的综合评分 (0-100分)
        # R²权重40%, ±5%准确率权重30%, MAPE权重30%
        if np.sum(non_zero_mask) > 0:
            score_lstm = (r2_lstm * 40 + (100 - mape_lstm) * 0.3 + acc_5_lstm * 0.3)
            score_kalman = (r2_kalman * 40 + (100 - mape_kalman) * 0.3 + acc_5_kalman * 0.3)
            
            print(f"  综合准确率评分 (满分100):")
            print(f"    LSTM: {score_lstm:.2f}分")
            print(f"    集成: {score_kalman:.2f}分")
            print(f"    提升: +{score_kalman - score_lstm:.2f}分")
        
        # 5. 计算公式总结
        print(f"\n📝 5. 计算公式总结")
        print(f"  MAE = mean(|真实值 - 预测值|)")
        print(f"  RMSE = sqrt(mean((真实值 - 预测值)²))")
        print(f"  MAPE = mean(|真实值 - 预测值| / |真实值|) × 100%")
        print(f"  ±X%准确率 = (相对误差≤X%的样本数 / 总样本数) × 100%")
        print(f"  R² = 1 - (残差平方和 / 总平方和)")
        print(f"  相关系数 = corrcoef(真实值, 预测值)")
        
        print(f"\n" + "="*60)
        print("预测平均准确率计算完成！")
        print("="*60)
        
    except FileNotFoundError:
        print(f"❌ 错误: 未找到文件 {csv_file}")
        print("请先运行 improve.py 生成预测结果")
    except Exception as e:
        print(f"❌ 计算过程中出错: {e}")

if __name__ == "__main__":
    calculate_comprehensive_accuracy()
